@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations for AI interface */
.typing-indicator {
  @apply flex space-x-1;
}

.typing-indicator span {
  @apply w-2 h-2 bg-primary rounded-full animate-pulse;
  animation-delay: calc(var(--i) * 0.2s);
}

.voice-pulse {
  @apply relative;
}

.voice-pulse::before {
  content: '';
  @apply absolute inset-0 rounded-full bg-red-500 animate-pulse-ring;
}

.waveform-bar {
  @apply bg-primary transition-all duration-150 ease-in-out;
  animation: waveform 1s ease-in-out infinite;
  animation-delay: calc(var(--i) * 0.1s);
}

.message-fade-in {
  @apply animate-fade-in;
}

.sidebar-slide {
  @apply animate-slide-in;
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-foreground;
}

/* File upload drag and drop styling */
.drag-over {
  @apply border-2 border-dashed border-primary bg-primary/10;
}

/* Chart container styling */
.chart-container {
  @apply relative w-full h-64 bg-card rounded-lg border p-4;
}

/* Cost meter styling */
.cost-meter {
  @apply relative w-full h-2 bg-muted rounded-full overflow-hidden;
}

.cost-fill {
  @apply h-full transition-all duration-300 ease-out;
}

/* Agent status indicators */
.agent-status-active {
  @apply bg-green-500;
}

.agent-status-idle {
  @apply bg-yellow-500;
}

.agent-status-busy {
  @apply bg-blue-500;
}

.agent-status-error {
  @apply bg-red-500;
}

/* Markdown content styling */
.markdown-content {
  @apply prose prose-sm dark:prose-invert max-w-none;
}

.markdown-content pre {
  @apply bg-muted p-4 rounded-lg overflow-x-auto;
}

.markdown-content code {
  @apply bg-muted px-1 py-0.5 rounded text-sm;
}

.markdown-content table {
  @apply border-collapse border border-border;
}

.markdown-content th,
.markdown-content td {
  @apply border border-border px-4 py-2;
}

.markdown-content th {
  @apply bg-muted font-semibold;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
