version: '3.8'

services:
  # Development frontend service
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - "5173:5173"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_AGENT_SERVICE=http://localhost:8001
      - VITE_VOICE_SERVICE=http://localhost:8002
      - VITE_WS_HOST=localhost:8000
      - VITE_ENABLE_VOICE=true
      - VITE_ENABLE_STREAMING=true
      - VITE_ENABLE_DEBUG=true
    networks:
      - agentium-network
    profiles:
      - development

  # Production frontend service
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "80:80"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    networks:
      - agentium-network
    profiles:
      - production
    depends_on:
      - backend
      - agent-service
      - voice-service

  # Backend service (placeholder - should be defined in main docker-compose)
  backend:
    image: agentium-backend:latest
    ports:
      - "8000:8000"
    networks:
      - agentium-network
    profiles:
      - production

  # Agent service (placeholder)
  agent-service:
    image: agentium-agent-service:latest
    ports:
      - "8001:8001"
    networks:
      - agentium-network
    profiles:
      - production

  # Voice service (placeholder)
  voice-service:
    image: agentium-voice-service:latest
    ports:
      - "8002:8002"
    networks:
      - agentium-network
    profiles:
      - production

networks:
  agentium-network:
    driver: bridge
    external: true
