{"name": "@vitest/eslint-plugin", "version": "1.3.4", "license": "MIT", "description": "Eslint plugin for vitest", "repository": "vitest-dev/eslint-plugin-vitest", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "vitest eslint plugin", "vitest", "eslint plugin"], "author": "Verite Mugabo <https://veritemugabo.com/>", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "exports": {".": {"module": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@typescript-eslint/utils": "^8.24.1"}, "devDependencies": {"@stylistic/eslint-plugin": "^2.13.0", "@types/eslint": "^9.6.1", "@types/mocha": "^10.0.10", "@types/node": "^22.13.1", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "@typescript-eslint/rule-tester": "^8.24.1", "@vitest/eslint-plugin": "^1.3.1", "bumpp": "^9.11.1", "concurrently": "^9.1.2", "eslint": "^9.20.0", "eslint-config-prettier": "^10.1.5", "eslint-doc-generator": "^2.0.2", "eslint-plugin-eslint-plugin": "^6.4.0", "eslint-remote-tester": "^4.0.1", "eslint-remote-tester-repositories": "^2.0.0", "prettier": "^3.5.3", "tsx": "^4.19.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^3.0.5"}, "peerDependencies": {"eslint": ">= 8.57.0", "typescript": ">= 5.0.0", "vitest": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vitest": {"optional": true}}, "prettier": {"semi": false, "singleQuote": true, "endOfLine": "auto"}, "scripts": {"build": "unbuild --config unbuild.config.ts", "lint:eslint-docs": "pnpm build && eslint-doc-generator --check", "lint:js": "eslint .", "lint": "concurrently --prefixColors auto \"pnpm:lint:*\"", "release": "bumpp package.json --commit --push --tag && pnpm build && pnpm publish", "stub": "unbuild --stub", "test:ci": "npm run format:check && vitest run", "format:check": "npx prettier 'src/**/*.ts' --check", "format:fix": "npx prettier '**/*.{ts,js}' --write", "test": "vitest", "update:eslint-docs": "pnpm build && eslint-doc-generator", "tsc": "tsc --noEmit"}}