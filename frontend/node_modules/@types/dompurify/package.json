{"name": "@types/dompurify", "version": "3.0.5", "description": "TypeScript definitions for dompurify", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/dompurify", "license": "MIT", "contributors": [{"name": "<PERSON> https://github.com/davetayls\n//                 <PERSON><PERSON>", "githubUsername": "bazuzi", "url": "https://github.com/bazuzi"}, {"name": "FlowCrypt", "githubUsername": "FlowCrypt", "url": "https://github.com/FlowCrypt"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Exigerr"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/NicholasEllul"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/dompurify"}, "scripts": {}, "dependencies": {"@types/trusted-types": "*"}, "typesPublisherContentHash": "f5501a1d31d5a7b2456704b3053c1c60efe0758bea38b4c3dbe3bd530638506e", "typeScriptVersion": "4.5"}