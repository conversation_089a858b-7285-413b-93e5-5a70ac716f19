import{d as r,y as m,i as a,R as l,S as i,T as p,e as _,o as f,u as b,U as d}from"./index-Dwf2ZVS8.js";const R=r({__name:"custom-tab-view",setup(v){const o=l(),s=p(),{flattenedTabs:n}=m(),u=a(()=>o.params.name),t=a(()=>n.value.find(e=>u.value===e.name)||null);return i(()=>{if(!t.value){const e=setTimeout(()=>{if(t.value){clearTimeout(e);return}s.replace("/overview")},2e3)}}),(e,T)=>{const c=d;return f(),_(c,{tab:b(t)},null,8,["tab"])}}});export{R as default};
