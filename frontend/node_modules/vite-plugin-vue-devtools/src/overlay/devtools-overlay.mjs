(function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Es(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const le={},on=[],Ke=()=>{},Oc=()=>!1,co=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),bs=e=>e.startsWith("onUpdate:"),be=Object.assign,ws=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ac=Object.prototype.hasOwnProperty,oe=(e,t)=>Ac.call(e,t),Y=Array.isArray,bn=e=>fo(e)==="[object Map]",Cc=e=>fo(e)==="[object Set]",K=e=>typeof e=="function",me=e=>typeof e=="string",Ht=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Qr=e=>(de(e)||K(e))&&K(e.then)&&K(e.catch),Dc=Object.prototype.toString,fo=e=>Dc.call(e),Pc=e=>fo(e).slice(8,-1),Ic=e=>fo(e)==="[object Object]",Ss=e=>me(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wn=Es(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),po=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Rc=/-(\w)/g,He=po(e=>e.replace(Rc,(t,n)=>n?n.toUpperCase():"")),kc=/\B([A-Z])/g,St=po(e=>e.replace(kc,"-$1").toLowerCase()),ho=po(e=>e.charAt(0).toUpperCase()+e.slice(1)),xs=po(e=>e?`on${ho(e)}`:""),xt=(e,t)=>!Object.is(e,t),Ts=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ei=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},Nc=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ti;const _o=()=>ti||(ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ne(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=me(o)?$c(o):Ne(o);if(s)for(const r in s)t[r]=s[r]}return t}else if(me(e)||de(e))return e}const Vc=/;(?![^(]*\))/g,Lc=/:([^]+)/,Mc=/\/\*[^]*?\*\//g;function $c(e){const t={};return e.replace(Mc,"").split(Vc).forEach(n=>{if(n){const o=n.split(Lc);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function ht(e){let t="";if(me(e))t=e;else if(Y(e))for(let n=0;n<e.length;n++){const o=ht(e[n]);o&&(t+=o+" ")}else if(de(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Uc(e){if(!e)return null;let{class:t,style:n}=e;return t&&!me(t)&&(e.class=ht(t)),n&&(e.style=Ne(n)),e}const Fc=Es("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ni(e){return!!e||e===""}var Bc={TERM_PROGRAM:"vscode",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_config_registry:"https://registry.npmjs.org/",USER:"arlo",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_verify_deps_before_run:"false",npm_config_frozen_lockfile:"",npm_config_catalog:'{"@iconify/json":"^2.2.321","@types/node":"^22.13.14","@unocss/reset":"^66.0.0","@vitejs/plugin-vue":"^5.2.3","@vueuse/core":"^12.8.2","@vueuse/integrations":"^12.8.2","colord":"^2.9.3","execa":"^9.5.2","floating-vue":"5.2.2","mitt":"^3.0.1","pathe":"^2.0.3","perfect-debounce":"^1.0.0","pinia":"^3.0.1","sass-embedded":"^1.86.0","serve":"^14.2.4","shiki":"^3.2.1","splitpanes":"^4.0.3","typescript":"^5.8.2","unocss":"^66.0.0","unplugin-auto-import":"^19.1.2","vite":"^6.2.1","vite-hot-client":"^2.0.4","vite-plugin-dts":"^4.5.3","vite-plugin-inspect":"0.8.9","vue":"^3.5.13","vue-router":"^4.5.0","vue-virtual-scroller":"2.0.0-beta.8"}',PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/10.7.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.local/state/fnm_multishells/35137_1749985742518/bin:/opt/homebrew/bin:/opt/homebrew/opt/python@3.13/libexec/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/.local/state/fnm_multishells/35050_1749985741258/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/libexec/bin:/Applications/WebStorm.app/Contents/MacOS:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_lifecycle_event:"build",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"c0a2fff44f360831",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_version:"7.7.7",VSCODE_INJECTION:"1",HOME:"/Users/<USER>",SHLVL:"0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_lifecycle_script:"vite build",VSCODE_GIT_IPC_HANDLE:"/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/vscode-git-92667dda0d.sock",npm_config_user_agent:"pnpm/10.7.0 npm/? node/v20.17.0 darwin arm64",VSCODE_GIT_ASKPASS_NODE:"/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};let Te;class Hc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Te,!t&&Te&&(this.index=(Te.scopes||(Te.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Te;try{return Te=this,t()}finally{Te=n}}}on(){Te=this}off(){Te=this.parent}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function oi(){return Te}function zc(e,t=!1){Te&&Te.cleanups.push(e)}let ae;const Os=new WeakSet;class si{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Te&&Te.active&&Te.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Os.has(this)&&(Os.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ii(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,fi(this),li(this);const t=ae,n=We;ae=this,We=!0;try{return this.fn()}finally{ui(this),ae=t,We=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ps(t);this.deps=this.depsTail=void 0,fi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Os.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ds(this)&&this.run()}get dirty(){return Ds(this)}}let ri=0,Sn,xn;function ii(e,t=!1){if(e.flags|=8,t){e.next=xn,xn=e;return}e.next=Sn,Sn=e}function As(){ri++}function Cs(){if(--ri>0)return;if(xn){let t=xn;for(xn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Sn;){let t=Sn;for(Sn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function li(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ui(e){let t,n=e.depsTail,o=n;for(;o;){const s=o.prevDep;o.version===-1?(o===n&&(n=s),Ps(o),jc(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=s}e.deps=t,e.depsTail=n}function Ds(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ai(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ai(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Tn))return;e.globalVersion=Tn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ds(e)){e.flags&=-3;return}const n=ae,o=We;ae=e,We=!0;try{li(e);const s=e.fn(e._value);(t.version===0||xt(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ae=n,We=o,ui(e),e.flags&=-3}}function Ps(e,t=!1){const{dep:n,prevSub:o,nextSub:s}=e;if(o&&(o.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Ps(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function jc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let We=!0;const ci=[];function _t(){ci.push(We),We=!1}function mt(){const e=ci.pop();We=e===void 0?!0:e}function fi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ae;ae=void 0;try{t()}finally{ae=n}}}let Tn=0;class Kc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class mo{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!We||ae===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ae)n=this.activeLink=new Kc(ae,this),ae.deps?(n.prevDep=ae.depsTail,ae.depsTail.nextDep=n,ae.depsTail=n):ae.deps=ae.depsTail=n,di(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=ae.depsTail,n.nextDep=void 0,ae.depsTail.nextDep=n,ae.depsTail=n,ae.deps===n&&(ae.deps=o)}return n}trigger(t){this.version++,Tn++,this.notify(t)}notify(t){As();try{Bc.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Cs()}}}function di(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)di(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const go=new WeakMap,zt=Symbol(""),Is=Symbol(""),On=Symbol("");function we(e,t,n){if(We&&ae){let o=go.get(e);o||go.set(e,o=new Map);let s=o.get(n);s||(o.set(n,s=new mo),s.map=o,s.key=n),s.track()}}function gt(e,t,n,o,s,r){const i=go.get(e);if(!i){Tn++;return}const l=u=>{u&&u.trigger()};if(As(),t==="clear")i.forEach(l);else{const u=Y(e),a=u&&Ss(n);if(u&&n==="length"){const c=Number(o);i.forEach((f,h)=>{(h==="length"||h===On||!Ht(h)&&h>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),a&&l(i.get(On)),t){case"add":u?a&&l(i.get("length")):(l(i.get(zt)),bn(e)&&l(i.get(Is)));break;case"delete":u||(l(i.get(zt)),bn(e)&&l(i.get(Is)));break;case"set":bn(e)&&l(i.get(zt));break}}Cs()}function Wc(e,t){const n=go.get(e);return n&&n.get(t)}function sn(e){const t=ne(e);return t===e?t:(we(t,"iterate",On),Ge(e)?t:t.map(Oe))}function Rs(e){return we(e=ne(e),"iterate",On),e}const Gc={__proto__:null,[Symbol.iterator](){return ks(this,Symbol.iterator,Oe)},concat(...e){return sn(this).concat(...e.map(t=>Y(t)?sn(t):t))},entries(){return ks(this,"entries",e=>(e[1]=Oe(e[1]),e))},every(e,t){return vt(this,"every",e,t,void 0,arguments)},filter(e,t){return vt(this,"filter",e,t,n=>n.map(Oe),arguments)},find(e,t){return vt(this,"find",e,t,Oe,arguments)},findIndex(e,t){return vt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return vt(this,"findLast",e,t,Oe,arguments)},findLastIndex(e,t){return vt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return vt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ns(this,"includes",e)},indexOf(...e){return Ns(this,"indexOf",e)},join(e){return sn(this).join(e)},lastIndexOf(...e){return Ns(this,"lastIndexOf",e)},map(e,t){return vt(this,"map",e,t,void 0,arguments)},pop(){return An(this,"pop")},push(...e){return An(this,"push",e)},reduce(e,...t){return pi(this,"reduce",e,t)},reduceRight(e,...t){return pi(this,"reduceRight",e,t)},shift(){return An(this,"shift")},some(e,t){return vt(this,"some",e,t,void 0,arguments)},splice(...e){return An(this,"splice",e)},toReversed(){return sn(this).toReversed()},toSorted(e){return sn(this).toSorted(e)},toSpliced(...e){return sn(this).toSpliced(...e)},unshift(...e){return An(this,"unshift",e)},values(){return ks(this,"values",Oe)}};function ks(e,t,n){const o=Rs(e),s=o[t]();return o!==e&&!Ge(e)&&(s._next=s.next,s.next=()=>{const r=s._next();return r.value&&(r.value=n(r.value)),r}),s}const Yc=Array.prototype;function vt(e,t,n,o,s,r){const i=Rs(e),l=i!==e&&!Ge(e),u=i[t];if(u!==Yc[t]){const f=u.apply(e,r);return l?Oe(f):f}let a=n;i!==e&&(l?a=function(f,h){return n.call(this,Oe(f),h,e)}:n.length>2&&(a=function(f,h){return n.call(this,f,h,e)}));const c=u.call(i,a,o);return l&&s?s(c):c}function pi(e,t,n,o){const s=Rs(e);let r=n;return s!==e&&(Ge(e)?n.length>3&&(r=function(i,l,u){return n.call(this,i,l,u,e)}):r=function(i,l,u){return n.call(this,i,Oe(l),u,e)}),s[t](r,...o)}function Ns(e,t,n){const o=ne(e);we(o,"iterate",On);const s=o[t](...n);return(s===-1||s===!1)&&Ls(n[0])?(n[0]=ne(n[0]),o[t](...n)):s}function An(e,t,n=[]){_t(),As();const o=ne(e)[t].apply(e,n);return Cs(),mt(),o}const qc=Es("__proto__,__v_isRef,__isVue"),hi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ht));function Xc(e){Ht(e)||(e=String(e));const t=ne(this);return we(t,"has",e),t.hasOwnProperty(e)}class _i{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return r;if(n==="__v_raw")return o===(s?r?bi:Ei:r?yi:vi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=Y(t);if(!s){let u;if(i&&(u=Gc[n]))return u;if(n==="hasOwnProperty")return Xc}const l=Reflect.get(t,n,ve(t)?t:o);return(Ht(n)?hi.has(n):qc(n))||(s||we(t,"get",n),r)?l:ve(l)?i&&Ss(n)?l:l.value:de(l)?s?Cn(l):rn(l):l}}class mi extends _i{constructor(t=!1){super(!1,t)}set(t,n,o,s){let r=t[n];if(!this._isShallow){const u=jt(r);if(!Ge(o)&&!jt(o)&&(r=ne(r),o=ne(o)),!Y(t)&&ve(r)&&!ve(o))return u?!1:(r.value=o,!0)}const i=Y(t)&&Ss(n)?Number(n)<t.length:oe(t,n),l=Reflect.set(t,n,o,ve(t)?t:s);return t===ne(s)&&(i?xt(o,r)&&gt(t,"set",n,o):gt(t,"add",n,o)),l}deleteProperty(t,n){const o=oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&o&&gt(t,"delete",n,void 0),s}has(t,n){const o=Reflect.has(t,n);return(!Ht(n)||!hi.has(n))&&we(t,"has",n),o}ownKeys(t){return we(t,"iterate",Y(t)?"length":zt),Reflect.ownKeys(t)}}class gi extends _i{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Zc=new mi,Jc=new gi,Qc=new mi(!0),ef=new gi(!0),Vs=e=>e,vo=e=>Reflect.getPrototypeOf(e);function tf(e,t,n){return function(...o){const s=this.__v_raw,r=ne(s),i=bn(r),l=e==="entries"||e===Symbol.iterator&&i,u=e==="keys"&&i,a=s[e](...o),c=n?Vs:t?Ms:Oe;return!t&&we(r,"iterate",u?Is:zt),{next(){const{value:f,done:h}=a.next();return h?{value:f,done:h}:{value:l?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function yo(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function nf(e,t){const n={get(s){const r=this.__v_raw,i=ne(r),l=ne(s);e||(xt(s,l)&&we(i,"get",s),we(i,"get",l));const{has:u}=vo(i),a=t?Vs:e?Ms:Oe;if(u.call(i,s))return a(r.get(s));if(u.call(i,l))return a(r.get(l));r!==i&&r.get(s)},get size(){const s=this.__v_raw;return!e&&we(ne(s),"iterate",zt),Reflect.get(s,"size",s)},has(s){const r=this.__v_raw,i=ne(r),l=ne(s);return e||(xt(s,l)&&we(i,"has",s),we(i,"has",l)),s===l?r.has(s):r.has(s)||r.has(l)},forEach(s,r){const i=this,l=i.__v_raw,u=ne(l),a=t?Vs:e?Ms:Oe;return!e&&we(u,"iterate",zt),l.forEach((c,f)=>s.call(r,a(c),a(f),i))}};return be(n,e?{add:yo("add"),set:yo("set"),delete:yo("delete"),clear:yo("clear")}:{add(s){!t&&!Ge(s)&&!jt(s)&&(s=ne(s));const r=ne(this);return vo(r).has.call(r,s)||(r.add(s),gt(r,"add",s,s)),this},set(s,r){!t&&!Ge(r)&&!jt(r)&&(r=ne(r));const i=ne(this),{has:l,get:u}=vo(i);let a=l.call(i,s);a||(s=ne(s),a=l.call(i,s));const c=u.call(i,s);return i.set(s,r),a?xt(r,c)&&gt(i,"set",s,r):gt(i,"add",s,r),this},delete(s){const r=ne(this),{has:i,get:l}=vo(r);let u=i.call(r,s);u||(s=ne(s),u=i.call(r,s)),l&&l.call(r,s);const a=r.delete(s);return u&&gt(r,"delete",s,void 0),a},clear(){const s=ne(this),r=s.size!==0,i=s.clear();return r&&gt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=tf(s,e,t)}),n}function Eo(e,t){const n=nf(e,t);return(o,s,r)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?o:Reflect.get(oe(n,s)&&s in o?n:o,s,r)}const of={get:Eo(!1,!1)},sf={get:Eo(!1,!0)},rf={get:Eo(!0,!1)},lf={get:Eo(!0,!0)},vi=new WeakMap,yi=new WeakMap,Ei=new WeakMap,bi=new WeakMap;function uf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function af(e){return e.__v_skip||!Object.isExtensible(e)?0:uf(Pc(e))}function rn(e){return jt(e)?e:wo(e,!1,Zc,of,vi)}function cf(e){return wo(e,!1,Qc,sf,yi)}function Cn(e){return wo(e,!0,Jc,rf,Ei)}function bo(e){return wo(e,!0,ef,lf,bi)}function wo(e,t,n,o,s){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=s.get(e);if(r)return r;const i=af(e);if(i===0)return e;const l=new Proxy(e,i===2?o:n);return s.set(e,l),l}function Dn(e){return jt(e)?Dn(e.__v_raw):!!(e&&e.__v_isReactive)}function jt(e){return!!(e&&e.__v_isReadonly)}function Ge(e){return!!(e&&e.__v_isShallow)}function Ls(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function ff(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&ei(e,"__v_skip",!0),e}const Oe=e=>de(e)?rn(e):e,Ms=e=>de(e)?Cn(e):e;function ve(e){return e?e.__v_isRef===!0:!1}function Ve(e){return wi(e,!1)}function Le(e){return wi(e,!0)}function wi(e,t){return ve(e)?e:new df(e,t)}class df{constructor(t,n){this.dep=new mo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ne(t),this._value=n?t:Oe(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||Ge(t)||jt(t);t=o?t:ne(t),xt(t,n)&&(this._rawValue=t,this._value=o?t:Oe(t),this.dep.trigger())}}function J(e){return ve(e)?e.value:e}function Ae(e){return K(e)?e():J(e)}const pf={get:(e,t,n)=>t==="__v_raw"?e:J(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const s=e[t];return ve(s)&&!ve(n)?(s.value=n,!0):Reflect.set(e,t,n,o)}};function Si(e){return Dn(e)?e:new Proxy(e,pf)}class hf{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new mo,{get:o,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=o,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function _f(e){return new hf(e)}class mf{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Wc(ne(this._object),this._key)}}class gf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function vf(e,t,n){return ve(e)?e:K(e)?new gf(e):de(e)&&arguments.length>1?yf(e,t,n):Ve(e)}function yf(e,t,n){const o=e[t];return ve(o)?o:new mf(e,t,n)}class Ef{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new mo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Tn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return ii(this,!0),!0}get value(){const t=this.dep.track();return ai(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function bf(e,t,n=!1){let o,s;return K(e)?o=e:(o=e.get,s=e.set),new Ef(o,s,n)}const So={},xo=new WeakMap;let Kt;function wf(e,t=!1,n=Kt){if(n){let o=xo.get(n);o||xo.set(n,o=[]),o.push(e)}}function Sf(e,t,n=le){const{immediate:o,deep:s,once:r,scheduler:i,augmentJob:l,call:u}=n,a=b=>s?b:Ge(b)||s===!1||s===0?yt(b,1):yt(b);let c,f,h,d,_=!1,v=!1;if(ve(e)?(f=()=>e.value,_=Ge(e)):Dn(e)?(f=()=>a(e),_=!0):Y(e)?(v=!0,_=e.some(b=>Dn(b)||Ge(b)),f=()=>e.map(b=>{if(ve(b))return b.value;if(Dn(b))return a(b);if(K(b))return u?u(b,2):b()})):K(e)?t?f=u?()=>u(e,2):e:f=()=>{if(h){_t();try{h()}finally{mt()}}const b=Kt;Kt=c;try{return u?u(e,3,[d]):e(d)}finally{Kt=b}}:f=Ke,t&&s){const b=f,P=s===!0?1/0:s;f=()=>yt(b(),P)}const y=oi(),g=()=>{c.stop(),y&&y.active&&ws(y.effects,c)};if(r&&t){const b=t;t=(...P)=>{b(...P),g()}}let x=v?new Array(e.length).fill(So):So;const D=b=>{if(!(!(c.flags&1)||!c.dirty&&!b))if(t){const P=c.run();if(s||_||(v?P.some((U,H)=>xt(U,x[H])):xt(P,x))){h&&h();const U=Kt;Kt=c;try{const H=[P,x===So?void 0:v&&x[0]===So?[]:x,d];u?u(t,3,H):t(...H),x=P}finally{Kt=U}}}else c.run()};return l&&l(D),c=new si(f),c.scheduler=i?()=>i(D,!1):D,d=b=>wf(b,!1,c),h=c.onStop=()=>{const b=xo.get(c);if(b){if(u)u(b,4);else for(const P of b)P();xo.delete(c)}},t?o?D(!0):x=c.run():i?i(D.bind(null,!0),!0):c.run(),g.pause=c.pause.bind(c),g.resume=c.resume.bind(c),g.stop=g,g}function yt(e,t=1/0,n){if(t<=0||!de(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ve(e))yt(e.value,t,n);else if(Y(e))for(let o=0;o<e.length;o++)yt(e[o],t,n);else if(Cc(e)||bn(e))e.forEach(o=>{yt(o,t,n)});else if(Ic(e)){for(const o in e)yt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&yt(e[o],t,n)}return e}var Tt={TERM_PROGRAM:"vscode",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_config_registry:"https://registry.npmjs.org/",USER:"arlo",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_verify_deps_before_run:"false",npm_config_frozen_lockfile:"",npm_config_catalog:'{"@iconify/json":"^2.2.321","@types/node":"^22.13.14","@unocss/reset":"^66.0.0","@vitejs/plugin-vue":"^5.2.3","@vueuse/core":"^12.8.2","@vueuse/integrations":"^12.8.2","colord":"^2.9.3","execa":"^9.5.2","floating-vue":"5.2.2","mitt":"^3.0.1","pathe":"^2.0.3","perfect-debounce":"^1.0.0","pinia":"^3.0.1","sass-embedded":"^1.86.0","serve":"^14.2.4","shiki":"^3.2.1","splitpanes":"^4.0.3","typescript":"^5.8.2","unocss":"^66.0.0","unplugin-auto-import":"^19.1.2","vite":"^6.2.1","vite-hot-client":"^2.0.4","vite-plugin-dts":"^4.5.3","vite-plugin-inspect":"0.8.9","vue":"^3.5.13","vue-router":"^4.5.0","vue-virtual-scroller":"2.0.0-beta.8"}',PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/10.7.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.local/state/fnm_multishells/35137_1749985742518/bin:/opt/homebrew/bin:/opt/homebrew/opt/python@3.13/libexec/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/.local/state/fnm_multishells/35050_1749985741258/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/libexec/bin:/Applications/WebStorm.app/Contents/MacOS:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_lifecycle_event:"build",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"c0a2fff44f360831",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_version:"7.7.7",VSCODE_INJECTION:"1",HOME:"/Users/<USER>",SHLVL:"0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_lifecycle_script:"vite build",VSCODE_GIT_IPC_HANDLE:"/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/vscode-git-92667dda0d.sock",npm_config_user_agent:"pnpm/10.7.0 npm/? node/v20.17.0 darwin arm64",VSCODE_GIT_ASKPASS_NODE:"/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};const Pn=[];let $s=!1;function xf(e,...t){if($s)return;$s=!0,_t();const n=Pn.length?Pn[Pn.length-1].component:null,o=n&&n.appContext.config.warnHandler,s=Tf();if(o)ln(o,n,11,[e+t.map(r=>{var i,l;return(l=(i=r.toString)==null?void 0:i.call(r))!=null?l:JSON.stringify(r)}).join(""),n&&n.proxy,s.map(({vnode:r})=>`at <${vl(n,r.type)}>`).join(`
`),s]);else{const r=[`[Vue warn]: ${e}`,...t];s.length&&r.push(`
`,...Of(s)),console.warn(...r)}mt(),$s=!1}function Tf(){let e=Pn[Pn.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}function Of(e){const t=[];return e.forEach((n,o)=>{t.push(...o===0?[]:[`
`],...Af(n))}),t}function Af({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=e.component?e.component.parent==null:!1,s=` at <${vl(e.component,e.type,o)}`,r=">"+n;return e.props?[s,...Cf(e.props),r]:[s+r]}function Cf(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(o=>{t.push(...xi(o,e[o]))}),n.length>3&&t.push(" ..."),t}function xi(e,t,n){return me(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:ve(t)?(t=xi(e,ne(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):K(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=ne(t),n?t:[`${e}=`,t])}function ln(e,t,n,o){try{return o?e(...o):e()}catch(s){To(s,t,n)}}function Qe(e,t,n,o){if(K(e)){const s=ln(e,t,n,o);return s&&Qr(s)&&s.catch(r=>{To(r,t,n)}),s}if(Y(e)){const s=[];for(let r=0;r<e.length;r++)s.push(Qe(e[r],t,n,o));return s}}function To(e,t,n,o=!0){const s=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||le;if(t){let l=t.parent;const u=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,u,a)===!1)return}l=l.parent}if(r){_t(),ln(r,null,10,[e,u,a]),mt();return}}Df(e,n,s,o,i)}function Df(e,t,n,o=!0,s=!1){if(s)throw e;console.error(e)}const Ce=[];let et=-1;const un=[];let Ot=null,an=0;const Ti=Promise.resolve();let Oo=null;function Ao(e){const t=Oo||Ti;return e?t.then(this?e.bind(this):e):t}function Pf(e){let t=et+1,n=Ce.length;for(;t<n;){const o=t+n>>>1,s=Ce[o],r=In(s);r<e||r===e&&s.flags&2?t=o+1:n=o}return t}function Us(e){if(!(e.flags&1)){const t=In(e),n=Ce[Ce.length-1];!n||!(e.flags&2)&&t>=In(n)?Ce.push(e):Ce.splice(Pf(t),0,e),e.flags|=1,Oi()}}function Oi(){Oo||(Oo=Ti.then(Di))}function If(e){Y(e)?un.push(...e):Ot&&e.id===-1?Ot.splice(an+1,0,e):e.flags&1||(un.push(e),e.flags|=1),Oi()}function Ai(e,t,n=et+1){for(;n<Ce.length;n++){const o=Ce[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Ce.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Ci(e){if(un.length){const t=[...new Set(un)].sort((n,o)=>In(n)-In(o));if(un.length=0,Ot){Ot.push(...t);return}for(Ot=t,an=0;an<Ot.length;an++){const n=Ot[an];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Ot=null,an=0}}const In=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Di(e){const t=Ke;try{for(et=0;et<Ce.length;et++){const n=Ce[et];n&&!(n.flags&8)&&(Tt.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),ln(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;et<Ce.length;et++){const n=Ce[et];n&&(n.flags&=-2)}et=-1,Ce.length=0,Ci(),Oo=null,(Ce.length||un.length)&&Di()}}let ye=null,Co=null;function Do(e){const t=ye;return ye=e,Co=e&&e.type.__scopeId||null,t}function Rf(e){Co=e}function kf(){Co=null}const Nf=e=>Po;function Po(e,t=ye,n){if(!t||e._n)return e;const o=(...s)=>{o._d&&ul(-1);const r=Do(t);let i;try{i=e(...s)}finally{Do(r),o._d&&ul(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function tt(e,t){if(ye===null)return e;const n=Fo(ye),o=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[r,i,l,u=le]=t[s];r&&(K(r)&&(r={mounted:r,updated:r}),r.deep&&yt(i),o.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:u}))}return e}function Wt(e,t,n,o){const s=e.dirs,r=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];r&&(l.oldValue=r[i].value);let u=l.dir[o];u&&(_t(),Qe(u,n,8,[e.el,l,e,t]),mt())}}const Vf=Symbol("_vte"),Lf=e=>e.__isTeleport;function Fs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Fs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function cn(e,t){return K(e)?be({name:e.name},t,{setup:e}):e}function Pi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Io(e,t,n,o,s=!1){if(Y(e)){e.forEach((_,v)=>Io(_,t&&(Y(t)?t[v]:t),n,o,s));return}if(fn(o)&&!s){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&Io(e,t,n,o.component.subTree);return}const r=o.shapeFlag&4?Fo(o.component):o.el,i=s?null:r,{i:l,r:u}=e,a=t&&t.r,c=l.refs===le?l.refs={}:l.refs,f=l.setupState,h=ne(f),d=f===le?()=>!1:_=>oe(h,_);if(a!=null&&a!==u&&(me(a)?(c[a]=null,d(a)&&(f[a]=null)):ve(a)&&(a.value=null)),K(u))ln(u,l,12,[i,c]);else{const _=me(u),v=ve(u);if(_||v){const y=()=>{if(e.f){const g=_?d(u)?f[u]:c[u]:u.value;s?Y(g)&&ws(g,r):Y(g)?g.includes(r)||g.push(r):_?(c[u]=[r],d(u)&&(f[u]=c[u])):(u.value=[r],e.k&&(c[e.k]=u.value))}else _?(c[u]=i,d(u)&&(f[u]=i)):v&&(u.value=i,e.k&&(c[e.k]=i))};i?(y.id=-1,Me(y,n)):y()}}}_o().requestIdleCallback,_o().cancelIdleCallback;const fn=e=>!!e.type.__asyncLoader,Ii=e=>e.type.__isKeepAlive;function Mf(e,t){Ri(e,"a",t)}function $f(e,t){Ri(e,"da",t)}function Ri(e,t,n=ge){const o=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Ro(t,o,n),n){let s=n.parent;for(;s&&s.parent;)Ii(s.parent.vnode)&&Uf(o,t,n,s),s=s.parent}}function Uf(e,t,n,o){const s=Ro(t,e,o,!0);ki(()=>{ws(o[t],s)},n)}function Ro(e,t,n=ge,o=!1){if(n){const s=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{_t();const l=Fn(n),u=Qe(t,n,e,i);return l(),mt(),u});return o?s.unshift(r):s.push(r),r}}const Et=e=>(t,n=ge)=>{(!Bn||e==="sp")&&Ro(e,(...o)=>t(...o),n)},Ff=Et("bm"),Rn=Et("m"),Bf=Et("bu"),Hf=Et("u"),zf=Et("bum"),ki=Et("um"),jf=Et("sp"),Kf=Et("rtg"),Wf=Et("rtc");function Gf(e,t=ge){Ro("ec",e,t)}const Yf="components";function Bs(e,t){return Xf(Yf,e,!0,t)||e}const qf=Symbol.for("v-ndc");function Xf(e,t,n=!0,o=!1){const s=ye||ge;if(s){const r=s.type;{const l=gl(r,!1);if(l&&(l===t||l===He(t)||l===ho(He(t))))return r}const i=Ni(s[e]||r[e],t)||Ni(s.appContext[e],t);return!i&&o?r:i}}function Ni(e,t){return e&&(e[t]||e[He(t)]||e[ho(He(t))])}function ko(e,t,n={},o,s){if(ye.ce||ye.parent&&fn(ye.parent)&&ye.parent.ce)return t!=="default"&&(n.name=t),Re(),dn(Ie,null,[Se("slot",n,o)],64);let r=e[t];r&&r._c&&(r._d=!1),Re();const i=r&&Vi(r(n)),l=n.key||i&&i.key,u=dn(Ie,{key:(l&&!Ht(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),r&&r._c&&(r._d=!0),u}function Vi(e){return e.some(t=>$n(t)?!(t.type===At||t.type===Ie&&!Vi(t.children)):!0)?e:null}const Hs=e=>e?hl(e)?Fo(e):Hs(e.parent):null,kn=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Hs(e.parent),$root:e=>Hs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ui(e),$forceUpdate:e=>e.f||(e.f=()=>{Us(e.update)}),$nextTick:e=>e.n||(e.n=Ao.bind(e.proxy)),$watch:e=>vd.bind(e)}),zs=(e,t)=>e!==le&&!e.__isScriptSetup&&oe(e,t),Zf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:s,props:r,accessCache:i,type:l,appContext:u}=e;let a;if(t[0]!=="$"){const d=i[t];if(d!==void 0)switch(d){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return r[t]}else{if(zs(o,t))return i[t]=1,o[t];if(s!==le&&oe(s,t))return i[t]=2,s[t];if((a=e.propsOptions[0])&&oe(a,t))return i[t]=3,r[t];if(n!==le&&oe(n,t))return i[t]=4,n[t];js&&(i[t]=0)}}const c=kn[t];let f,h;if(c)return t==="$attrs"&&we(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==le&&oe(n,t))return i[t]=4,n[t];if(h=u.config.globalProperties,oe(h,t))return h[t]},set({_:e},t,n){const{data:o,setupState:s,ctx:r}=e;return zs(s,t)?(s[t]=n,!0):o!==le&&oe(o,t)?(o[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:r}},i){let l;return!!n[i]||e!==le&&oe(e,i)||zs(t,i)||(l=r[0])&&oe(l,i)||oe(o,i)||oe(kn,i)||oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Li(e){return Y(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let js=!0;function Jf(e){const t=Ui(e),n=e.proxy,o=e.ctx;js=!1,t.beforeCreate&&Mi(t.beforeCreate,e,"bc");const{data:s,computed:r,methods:i,watch:l,provide:u,inject:a,created:c,beforeMount:f,mounted:h,beforeUpdate:d,updated:_,activated:v,deactivated:y,beforeDestroy:g,beforeUnmount:x,destroyed:D,unmounted:b,render:P,renderTracked:U,renderTriggered:H,errorCaptured:W,serverPrefetch:$,expose:I,inheritAttrs:R,components:F,directives:z,filters:Q}=t;if(a&&Qf(a,o,null),i)for(const L in i){const X=i[L];K(X)&&(o[L]=X.bind(n))}if(s){const L=s.call(n,n);de(L)&&(e.data=rn(L))}if(js=!0,r)for(const L in r){const X=r[L],ue=K(X)?X.bind(n,n):K(X.get)?X.get.bind(n,n):Ke,Ue=!K(X)&&K(X.set)?X.set.bind(n):Ke,xe=_e({get:ue,set:Ue});Object.defineProperty(o,L,{enumerable:!0,configurable:!0,get:()=>xe.value,set:he=>xe.value=he})}if(l)for(const L in l)$i(l[L],o,n,L);if(u){const L=K(u)?u.call(n):u;Reflect.ownKeys(L).forEach(X=>{rd(X,L[X])})}c&&Mi(c,e,"c");function Z(L,X){Y(X)?X.forEach(ue=>L(ue.bind(n))):X&&L(X.bind(n))}if(Z(Ff,f),Z(Rn,h),Z(Bf,d),Z(Hf,_),Z(Mf,v),Z($f,y),Z(Gf,W),Z(Wf,U),Z(Kf,H),Z(zf,x),Z(ki,b),Z(jf,$),Y(I))if(I.length){const L=e.exposed||(e.exposed={});I.forEach(X=>{Object.defineProperty(L,X,{get:()=>n[X],set:ue=>n[X]=ue})})}else e.exposed||(e.exposed={});P&&e.render===Ke&&(e.render=P),R!=null&&(e.inheritAttrs=R),F&&(e.components=F),z&&(e.directives=z),$&&Pi(e)}function Qf(e,t,n=Ke){Y(e)&&(e=Ks(e));for(const o in e){const s=e[o];let r;de(s)?"default"in s?r=Vn(s.from||o,s.default,!0):r=Vn(s.from||o):r=Vn(s),ve(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[o]=r}}function Mi(e,t,n){Qe(Y(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function $i(e,t,n,o){let s=o.includes(".")?ol(n,o):()=>n[o];if(me(e)){const r=t[e];K(r)&&Ye(s,r)}else if(K(e))Ye(s,e.bind(n));else if(de(e))if(Y(e))e.forEach(r=>$i(r,t,n,o));else{const r=K(e.handler)?e.handler.bind(n):t[e.handler];K(r)&&Ye(s,r,e)}}function Ui(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let u;return l?u=l:!s.length&&!n&&!o?u=t:(u={},s.length&&s.forEach(a=>No(u,a,i,!0)),No(u,t,i)),de(t)&&r.set(t,u),u}function No(e,t,n,o=!1){const{mixins:s,extends:r}=t;r&&No(e,r,n,!0),s&&s.forEach(i=>No(e,i,n,!0));for(const i in t)if(!(o&&i==="expose")){const l=ed[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const ed={data:Fi,props:Bi,emits:Bi,methods:Nn,computed:Nn,beforeCreate:De,created:De,beforeMount:De,mounted:De,beforeUpdate:De,updated:De,beforeDestroy:De,beforeUnmount:De,destroyed:De,unmounted:De,activated:De,deactivated:De,errorCaptured:De,serverPrefetch:De,components:Nn,directives:Nn,watch:nd,provide:Fi,inject:td};function Fi(e,t){return t?e?function(){return be(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function td(e,t){return Nn(Ks(e),Ks(t))}function Ks(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function De(e,t){return e?[...new Set([].concat(e,t))]:t}function Nn(e,t){return e?be(Object.create(null),e,t):t}function Bi(e,t){return e?Y(e)&&Y(t)?[...new Set([...e,...t])]:be(Object.create(null),Li(e),Li(t??{})):t}function nd(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const o in t)n[o]=De(e[o],t[o]);return n}function Hi(){return{app:null,config:{isNativeTag:Oc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let od=0;function sd(e,t){return function(o,s=null){K(o)||(o=be({},o)),s!=null&&!de(s)&&(s=null);const r=Hi(),i=new WeakSet,l=[];let u=!1;const a=r.app={_uid:od++,_component:o,_props:s,_container:null,_context:r,_instance:null,version:Fd,get config(){return r.config},set config(c){},use(c,...f){return i.has(c)||(c&&K(c.install)?(i.add(c),c.install(a,...f)):K(c)&&(i.add(c),c(a,...f))),a},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),a},component(c,f){return f?(r.components[c]=f,a):r.components[c]},directive(c,f){return f?(r.directives[c]=f,a):r.directives[c]},mount(c,f,h){if(!u){const d=a._ceVNode||Se(o,s);return d.appContext=r,h===!0?h="svg":h===!1&&(h=void 0),e(d,c,h),u=!0,a._container=c,c.__vue_app__=a,Fo(d.component)}},onUnmount(c){l.push(c)},unmount(){u&&(Qe(l,a._instance,16),e(null,a._container),delete a._container.__vue_app__)},provide(c,f){return r.provides[c]=f,a},runWithContext(c){const f=Gt;Gt=a;try{return c()}finally{Gt=f}}};return a}}let Gt=null;function rd(e,t){if(ge){let n=ge.provides;const o=ge.parent&&ge.parent.provides;o===n&&(n=ge.provides=Object.create(o)),n[e]=t}}function Vn(e,t,n=!1){const o=ge||ye;if(o||Gt){const s=Gt?Gt._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&K(t)?t.call(o&&o.proxy):t}}function zi(){return!!(ge||ye||Gt)}const ji={},Ki=()=>Object.create(ji),Wi=e=>Object.getPrototypeOf(e)===ji;function id(e,t,n,o=!1){const s={},r=Ki();e.propsDefaults=Object.create(null),Gi(e,t,s,r);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=o?s:cf(s):e.type.props?e.props=s:e.props=r,e.attrs=r}function ld(e,t,n,o){const{props:s,attrs:r,vnode:{patchFlag:i}}=e,l=ne(s),[u]=e.propsOptions;let a=!1;if((o||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(Vo(e.emitsOptions,h))continue;const d=t[h];if(u)if(oe(r,h))d!==r[h]&&(r[h]=d,a=!0);else{const _=He(h);s[_]=Ws(u,l,_,d,e,!1)}else d!==r[h]&&(r[h]=d,a=!0)}}}else{Gi(e,t,s,r)&&(a=!0);let c;for(const f in l)(!t||!oe(t,f)&&((c=St(f))===f||!oe(t,c)))&&(u?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=Ws(u,l,f,void 0,e,!0)):delete s[f]);if(r!==l)for(const f in r)(!t||!oe(t,f))&&(delete r[f],a=!0)}a&&gt(e.attrs,"set","")}function Gi(e,t,n,o){const[s,r]=e.propsOptions;let i=!1,l;if(t)for(let u in t){if(wn(u))continue;const a=t[u];let c;s&&oe(s,c=He(u))?!r||!r.includes(c)?n[c]=a:(l||(l={}))[c]=a:Vo(e.emitsOptions,u)||(!(u in o)||a!==o[u])&&(o[u]=a,i=!0)}if(r){const u=ne(n),a=l||le;for(let c=0;c<r.length;c++){const f=r[c];n[f]=Ws(s,u,f,a[f],e,!oe(a,f))}}return i}function Ws(e,t,n,o,s,r){const i=e[n];if(i!=null){const l=oe(i,"default");if(l&&o===void 0){const u=i.default;if(i.type!==Function&&!i.skipFactory&&K(u)){const{propsDefaults:a}=s;if(n in a)o=a[n];else{const c=Fn(s);o=a[n]=u.call(null,t),c()}}else o=u;s.ce&&s.ce._setProp(n,o)}i[0]&&(r&&!l?o=!1:i[1]&&(o===""||o===St(n))&&(o=!0))}return o}const ud=new WeakMap;function Yi(e,t,n=!1){const o=n?ud:t.propsCache,s=o.get(e);if(s)return s;const r=e.props,i={},l=[];let u=!1;if(!K(e)){const c=f=>{u=!0;const[h,d]=Yi(f,t,!0);be(i,h),d&&l.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!u)return de(e)&&o.set(e,on),on;if(Y(r))for(let c=0;c<r.length;c++){const f=He(r[c]);qi(f)&&(i[f]=le)}else if(r)for(const c in r){const f=He(c);if(qi(f)){const h=r[c],d=i[f]=Y(h)||K(h)?{type:h}:be({},h),_=d.type;let v=!1,y=!0;if(Y(_))for(let g=0;g<_.length;++g){const x=_[g],D=K(x)&&x.name;if(D==="Boolean"){v=!0;break}else D==="String"&&(y=!1)}else v=K(_)&&_.name==="Boolean";d[0]=v,d[1]=y,(v||oe(d,"default"))&&l.push(f)}}const a=[i,l];return de(e)&&o.set(e,a),a}function qi(e){return e[0]!=="$"&&!wn(e)}const Xi=e=>e[0]==="_"||e==="$stable",Gs=e=>Y(e)?e.map(nt):[nt(e)],ad=(e,t,n)=>{if(t._n)return t;const o=Po((...s)=>(Tt.NODE_ENV!=="production"&&ge&&(!n||(n.root,ge.root)),Gs(t(...s))),n);return o._c=!1,o},Zi=(e,t,n)=>{const o=e._ctx;for(const s in e){if(Xi(s))continue;const r=e[s];if(K(r))t[s]=ad(s,r,o);else if(r!=null){const i=Gs(r);t[s]=()=>i}}},Ji=(e,t)=>{const n=Gs(t);e.slots.default=()=>n},Qi=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},cd=(e,t,n)=>{const o=e.slots=Ki();if(e.vnode.shapeFlag&32){const s=t._;s?(Qi(o,t,n),n&&ei(o,"_",s,!0)):Zi(t,o)}else t&&Ji(e,t)},fd=(e,t,n)=>{const{vnode:o,slots:s}=e;let r=!0,i=le;if(o.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:Qi(s,t,n):(r=!t.$stable,Zi(t,s)),i=t}else t&&(Ji(e,t),i={default:1});if(r)for(const l in s)!Xi(l)&&i[l]==null&&delete s[l]},Me=Td;function dd(e){return pd(e)}function pd(e,t){const n=_o();n.__VUE__=!0;const{insert:o,remove:s,patchProp:r,createElement:i,createText:l,createComment:u,setText:a,setElementText:c,parentNode:f,nextSibling:h,setScopeId:d=Ke,insertStaticContent:_}=e,v=(p,m,E,A=null,S=null,T=null,V=void 0,N=null,k=!!m.dynamicChildren)=>{if(p===m)return;p&&!Un(p,m)&&(A=ys(p),he(p,S,T,!0),p=null),m.patchFlag===-2&&(k=!1,m.dynamicChildren=null);const{type:C,ref:j,shapeFlag:M}=m;switch(C){case Lo:y(p,m,E,A);break;case At:g(p,m,E,A);break;case Zs:p==null&&x(m,E,A,V);break;case Ie:F(p,m,E,A,S,T,V,N,k);break;default:M&1?P(p,m,E,A,S,T,V,N,k):M&6?z(p,m,E,A,S,T,V,N,k):(M&64||M&128)&&C.process(p,m,E,A,S,T,V,N,k,uo)}j!=null&&S&&Io(j,p&&p.ref,T,m||p,!m)},y=(p,m,E,A)=>{if(p==null)o(m.el=l(m.children),E,A);else{const S=m.el=p.el;m.children!==p.children&&a(S,m.children)}},g=(p,m,E,A)=>{p==null?o(m.el=u(m.children||""),E,A):m.el=p.el},x=(p,m,E,A)=>{[p.el,p.anchor]=_(p.children,m,E,A,p.el,p.anchor)},D=({el:p,anchor:m},E,A)=>{let S;for(;p&&p!==m;)S=h(p),o(p,E,A),p=S;o(m,E,A)},b=({el:p,anchor:m})=>{let E;for(;p&&p!==m;)E=h(p),s(p),p=E;s(m)},P=(p,m,E,A,S,T,V,N,k)=>{m.type==="svg"?V="svg":m.type==="math"&&(V="mathml"),p==null?U(m,E,A,S,T,V,N,k):$(p,m,S,T,V,N,k)},U=(p,m,E,A,S,T,V,N)=>{let k,C;const{props:j,shapeFlag:M,transition:B,dirs:G}=p;if(k=p.el=i(p.type,T,j&&j.is,j),M&8?c(k,p.children):M&16&&W(p.children,k,null,A,S,Ys(p,T),V,N),G&&Wt(p,null,A,"created"),H(k,p,p.scopeId,V,A),j){for(const fe in j)fe!=="value"&&!wn(fe)&&r(k,fe,null,j[fe],T,A);"value"in j&&r(k,"value",null,j.value,T),(C=j.onVnodeBeforeMount)&&ot(C,A,p)}G&&Wt(p,null,A,"beforeMount");const te=hd(S,B);te&&B.beforeEnter(k),o(k,m,E),((C=j&&j.onVnodeMounted)||te||G)&&Me(()=>{C&&ot(C,A,p),te&&B.enter(k),G&&Wt(p,null,A,"mounted")},S)},H=(p,m,E,A,S)=>{if(E&&d(p,E),A)for(let T=0;T<A.length;T++)d(p,A[T]);if(S){let T=S.subTree;if(m===T||ll(T.type)&&(T.ssContent===m||T.ssFallback===m)){const V=S.vnode;H(p,V,V.scopeId,V.slotScopeIds,S.parent)}}},W=(p,m,E,A,S,T,V,N,k=0)=>{for(let C=k;C<p.length;C++){const j=p[C]=N?Dt(p[C]):nt(p[C]);v(null,j,m,E,A,S,T,V,N)}},$=(p,m,E,A,S,T,V)=>{const N=m.el=p.el;let{patchFlag:k,dynamicChildren:C,dirs:j}=m;k|=p.patchFlag&16;const M=p.props||le,B=m.props||le;let G;if(E&&Yt(E,!1),(G=B.onVnodeBeforeUpdate)&&ot(G,E,m,p),j&&Wt(m,p,E,"beforeUpdate"),E&&Yt(E,!0),(M.innerHTML&&B.innerHTML==null||M.textContent&&B.textContent==null)&&c(N,""),C?I(p.dynamicChildren,C,N,E,A,Ys(m,S),T):V||X(p,m,N,null,E,A,Ys(m,S),T,!1),k>0){if(k&16)R(N,M,B,E,S);else if(k&2&&M.class!==B.class&&r(N,"class",null,B.class,S),k&4&&r(N,"style",M.style,B.style,S),k&8){const te=m.dynamicProps;for(let fe=0;fe<te.length;fe++){const re=te[fe],Fe=M[re],ke=B[re];(ke!==Fe||re==="value")&&r(N,re,Fe,ke,S,E)}}k&1&&p.children!==m.children&&c(N,m.children)}else!V&&C==null&&R(N,M,B,E,S);((G=B.onVnodeUpdated)||j)&&Me(()=>{G&&ot(G,E,m,p),j&&Wt(m,p,E,"updated")},A)},I=(p,m,E,A,S,T,V)=>{for(let N=0;N<m.length;N++){const k=p[N],C=m[N],j=k.el&&(k.type===Ie||!Un(k,C)||k.shapeFlag&70)?f(k.el):E;v(k,C,j,null,A,S,T,V,!0)}},R=(p,m,E,A,S)=>{if(m!==E){if(m!==le)for(const T in m)!wn(T)&&!(T in E)&&r(p,T,m[T],null,S,A);for(const T in E){if(wn(T))continue;const V=E[T],N=m[T];V!==N&&T!=="value"&&r(p,T,N,V,S,A)}"value"in E&&r(p,"value",m.value,E.value,S)}},F=(p,m,E,A,S,T,V,N,k)=>{const C=m.el=p?p.el:l(""),j=m.anchor=p?p.anchor:l("");let{patchFlag:M,dynamicChildren:B,slotScopeIds:G}=m;G&&(N=N?N.concat(G):G),p==null?(o(C,E,A),o(j,E,A),W(m.children||[],E,j,S,T,V,N,k)):M>0&&M&64&&B&&p.dynamicChildren?(I(p.dynamicChildren,B,E,S,T,V,N),(m.key!=null||S&&m===S.subTree)&&el(p,m,!0)):X(p,m,E,j,S,T,V,N,k)},z=(p,m,E,A,S,T,V,N,k)=>{m.slotScopeIds=N,p==null?m.shapeFlag&512?S.ctx.activate(m,E,A,V,k):Q(m,E,A,S,T,V,k):Ee(p,m,k)},Q=(p,m,E,A,S,T,V)=>{const N=p.component=Id(p,A,S);if(Ii(p)&&(N.ctx.renderer=uo),Rd(N,!1,V),N.asyncDep){if(S&&S.registerDep(N,Z,V),!p.el){const k=N.subTree=Se(At);g(null,k,m,E)}}else Z(N,p,m,E,S,T,V)},Ee=(p,m,E)=>{const A=m.component=p.component;if(Sd(p,m,E))if(A.asyncDep&&!A.asyncResolved){L(A,m,E);return}else A.next=m,A.update();else m.el=p.el,A.vnode=m},Z=(p,m,E,A,S,T,V)=>{const N=()=>{if(p.isMounted){let{next:M,bu:B,u:G,parent:te,vnode:fe}=p;{const dt=tl(p);if(dt){M&&(M.el=fe.el,L(p,M,V)),dt.asyncDep.then(()=>{p.isUnmounted||N()});return}}let re=M,Fe;Yt(p,!1),M?(M.el=fe.el,L(p,M,V)):M=fe,B&&Ts(B),(Fe=M.props&&M.props.onVnodeBeforeUpdate)&&ot(Fe,te,M,fe),Yt(p,!0);const ke=rl(p),ft=p.subTree;p.subTree=ke,v(ft,ke,f(ft.el),ys(ft),p,S,T),M.el=ke.el,re===null&&xd(p,ke.el),G&&Me(G,S),(Fe=M.props&&M.props.onVnodeUpdated)&&Me(()=>ot(Fe,te,M,fe),S)}else{let M;const{el:B,props:G}=m,{bm:te,m:fe,parent:re,root:Fe,type:ke}=p,ft=fn(m);Yt(p,!1),te&&Ts(te),!ft&&(M=G&&G.onVnodeBeforeMount)&&ot(M,re,m),Yt(p,!0);{Fe.ce&&Fe.ce._injectChildStyle(ke);const dt=p.subTree=rl(p);v(null,dt,E,A,p,S,T),m.el=dt.el}if(fe&&Me(fe,S),!ft&&(M=G&&G.onVnodeMounted)){const dt=m;Me(()=>ot(M,re,dt),S)}(m.shapeFlag&256||re&&fn(re.vnode)&&re.vnode.shapeFlag&256)&&p.a&&Me(p.a,S),p.isMounted=!0,m=E=A=null}};p.scope.on();const k=p.effect=new si(N);p.scope.off();const C=p.update=k.run.bind(k),j=p.job=k.runIfDirty.bind(k);j.i=p,j.id=p.uid,k.scheduler=()=>Us(j),Yt(p,!0),C()},L=(p,m,E)=>{m.component=p;const A=p.vnode.props;p.vnode=m,p.next=null,ld(p,m.props,A,E),fd(p,m.children,E),_t(),Ai(p),mt()},X=(p,m,E,A,S,T,V,N,k=!1)=>{const C=p&&p.children,j=p?p.shapeFlag:0,M=m.children,{patchFlag:B,shapeFlag:G}=m;if(B>0){if(B&128){Ue(C,M,E,A,S,T,V,N,k);return}else if(B&256){ue(C,M,E,A,S,T,V,N,k);return}}G&8?(j&16&&lo(C,S,T),M!==C&&c(E,M)):j&16?G&16?Ue(C,M,E,A,S,T,V,N,k):lo(C,S,T,!0):(j&8&&c(E,""),G&16&&W(M,E,A,S,T,V,N,k))},ue=(p,m,E,A,S,T,V,N,k)=>{p=p||on,m=m||on;const C=p.length,j=m.length,M=Math.min(C,j);let B;for(B=0;B<M;B++){const G=m[B]=k?Dt(m[B]):nt(m[B]);v(p[B],G,E,null,S,T,V,N,k)}C>j?lo(p,S,T,!0,!1,M):W(m,E,A,S,T,V,N,k,M)},Ue=(p,m,E,A,S,T,V,N,k)=>{let C=0;const j=m.length;let M=p.length-1,B=j-1;for(;C<=M&&C<=B;){const G=p[C],te=m[C]=k?Dt(m[C]):nt(m[C]);if(Un(G,te))v(G,te,E,null,S,T,V,N,k);else break;C++}for(;C<=M&&C<=B;){const G=p[M],te=m[B]=k?Dt(m[B]):nt(m[B]);if(Un(G,te))v(G,te,E,null,S,T,V,N,k);else break;M--,B--}if(C>M){if(C<=B){const G=B+1,te=G<j?m[G].el:A;for(;C<=B;)v(null,m[C]=k?Dt(m[C]):nt(m[C]),E,te,S,T,V,N,k),C++}}else if(C>B)for(;C<=M;)he(p[C],S,T,!0),C++;else{const G=C,te=C,fe=new Map;for(C=te;C<=B;C++){const Be=m[C]=k?Dt(m[C]):nt(m[C]);Be.key!=null&&fe.set(Be.key,C)}let re,Fe=0;const ke=B-te+1;let ft=!1,dt=0;const ao=new Array(ke);for(C=0;C<ke;C++)ao[C]=0;for(C=G;C<=M;C++){const Be=p[C];if(Fe>=ke){he(Be,S,T,!0);continue}let pt;if(Be.key!=null)pt=fe.get(Be.key);else for(re=te;re<=B;re++)if(ao[re-te]===0&&Un(Be,m[re])){pt=re;break}pt===void 0?he(Be,S,T,!0):(ao[pt-te]=C+1,pt>=dt?dt=pt:ft=!0,v(Be,m[pt],E,null,S,T,V,N,k),Fe++)}const xc=ft?_d(ao):on;for(re=xc.length-1,C=ke-1;C>=0;C--){const Be=te+C,pt=m[Be],Tc=Be+1<j?m[Be+1].el:A;ao[C]===0?v(null,pt,E,Tc,S,T,V,N,k):ft&&(re<0||C!==xc[re]?xe(pt,E,Tc,2):re--)}}},xe=(p,m,E,A,S=null)=>{const{el:T,type:V,transition:N,children:k,shapeFlag:C}=p;if(C&6){xe(p.component.subTree,m,E,A);return}if(C&128){p.suspense.move(m,E,A);return}if(C&64){V.move(p,m,E,uo);return}if(V===Ie){o(T,m,E);for(let M=0;M<k.length;M++)xe(k[M],m,E,A);o(p.anchor,m,E);return}if(V===Zs){D(p,m,E);return}if(A!==2&&C&1&&N)if(A===0)N.beforeEnter(T),o(T,m,E),Me(()=>N.enter(T),S);else{const{leave:M,delayLeave:B,afterLeave:G}=N,te=()=>o(T,m,E),fe=()=>{M(T,()=>{te(),G&&G()})};B?B(T,te,fe):fe()}else o(T,m,E)},he=(p,m,E,A=!1,S=!1)=>{const{type:T,props:V,ref:N,children:k,dynamicChildren:C,shapeFlag:j,patchFlag:M,dirs:B,cacheIndex:G}=p;if(M===-2&&(S=!1),N!=null&&Io(N,null,E,p,!0),G!=null&&(m.renderCache[G]=void 0),j&256){m.ctx.deactivate(p);return}const te=j&1&&B,fe=!fn(p);let re;if(fe&&(re=V&&V.onVnodeBeforeUnmount)&&ot(re,m,p),j&6)io(p.component,E,A);else{if(j&128){p.suspense.unmount(E,A);return}te&&Wt(p,null,m,"beforeUnmount"),j&64?p.type.remove(p,m,E,uo,A):C&&!C.hasOnce&&(T!==Ie||M>0&&M&64)?lo(C,m,E,!1,!0):(T===Ie&&M&384||!S&&j&16)&&lo(k,m,E),A&&Bt(p)}(fe&&(re=V&&V.onVnodeUnmounted)||te)&&Me(()=>{re&&ot(re,m,p),te&&Wt(p,null,m,"unmounted")},E)},Bt=p=>{const{type:m,el:E,anchor:A,transition:S}=p;if(m===Ie){vs(E,A);return}if(m===Zs){b(p);return}const T=()=>{s(E),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:V,delayLeave:N}=S,k=()=>V(E,T);N?N(p.el,T,k):k()}else T()},vs=(p,m)=>{let E;for(;p!==m;)E=h(p),s(p),p=E;s(m)},io=(p,m,E)=>{const{bum:A,scope:S,job:T,subTree:V,um:N,m:k,a:C}=p;nl(k),nl(C),A&&Ts(A),S.stop(),T&&(T.flags|=8,he(V,p,m,E)),N&&Me(N,m),Me(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},lo=(p,m,E,A=!1,S=!1,T=0)=>{for(let V=T;V<p.length;V++)he(p[V],m,E,A,S)},ys=p=>{if(p.shapeFlag&6)return ys(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),E=m&&m[Vf];return E?h(E):m};let Jr=!1;const Sc=(p,m,E)=>{p==null?m._vnode&&he(m._vnode,null,null,!0):v(m._vnode||null,p,m,null,null,null,E),m._vnode=p,Jr||(Jr=!0,Ai(),Ci(),Jr=!1)},uo={p:v,um:he,m:xe,r:Bt,mt:Q,mc:W,pc:X,pbc:I,n:ys,o:e};return{render:Sc,hydrate:void 0,createApp:sd(Sc)}}function Ys({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Yt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function hd(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function el(e,t,n=!1){const o=e.children,s=t.children;if(Y(o)&&Y(s))for(let r=0;r<o.length;r++){const i=o[r];let l=s[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[r]=Dt(s[r]),l.el=i.el),!n&&l.patchFlag!==-2&&el(i,l)),l.type===Lo&&(l.el=i.el)}}function _d(e){const t=e.slice(),n=[0];let o,s,r,i,l;const u=e.length;for(o=0;o<u;o++){const a=e[o];if(a!==0){if(s=n[n.length-1],e[s]<a){t[o]=s,n.push(o);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<a?r=l+1:i=l;a<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function tl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:tl(t)}function nl(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const md=Symbol.for("v-scx"),gd=()=>Vn(md);function qs(e,t){return Xs(e,null,t)}function Ye(e,t,n){return Xs(e,t,n)}function Xs(e,t,n=le){const{immediate:o,deep:s,flush:r,once:i}=n,l=be({},n),u=t&&o||!t&&r!=="post";let a;if(Bn){if(r==="sync"){const d=gd();a=d.__watcherHandles||(d.__watcherHandles=[])}else if(!u){const d=()=>{};return d.stop=Ke,d.resume=Ke,d.pause=Ke,d}}const c=ge;l.call=(d,_,v)=>Qe(d,c,_,v);let f=!1;r==="post"?l.scheduler=d=>{Me(d,c&&c.suspense)}:r!=="sync"&&(f=!0,l.scheduler=(d,_)=>{_?d():Us(d)}),l.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,c&&(d.id=c.uid,d.i=c))};const h=Sf(e,t,l);return Bn&&(a?a.push(h):u&&h()),h}function vd(e,t,n){const o=this.proxy,s=me(e)?e.includes(".")?ol(o,e):()=>o[e]:e.bind(o,o);let r;K(t)?r=t:(r=t.handler,n=t);const i=Fn(this),l=Xs(s,r.bind(o),n);return i(),l}function ol(e,t){const n=t.split(".");return()=>{let o=e;for(let s=0;s<n.length&&o;s++)o=o[n[s]];return o}}const yd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${He(t)}Modifiers`]||e[`${St(t)}Modifiers`];function Ed(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||le;let s=n;const r=t.startsWith("update:"),i=r&&yd(o,t.slice(7));i&&(i.trim&&(s=n.map(c=>me(c)?c.trim():c)),i.number&&(s=n.map(Nc)));let l,u=o[l=xs(t)]||o[l=xs(He(t))];!u&&r&&(u=o[l=xs(St(t))]),u&&Qe(u,e,6,s);const a=o[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Qe(a,e,6,s)}}function sl(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(s!==void 0)return s;const r=e.emits;let i={},l=!1;if(!K(e)){const u=a=>{const c=sl(a,t,!0);c&&(l=!0,be(i,c))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!r&&!l?(de(e)&&o.set(e,null),null):(Y(r)?r.forEach(u=>i[u]=null):be(i,r),de(e)&&o.set(e,i),i)}function Vo(e,t){return!e||!co(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,St(t))||oe(e,t))}function Kg(){}function rl(e){const{type:t,vnode:n,proxy:o,withProxy:s,propsOptions:[r],slots:i,attrs:l,emit:u,render:a,renderCache:c,props:f,data:h,setupState:d,ctx:_,inheritAttrs:v}=e,y=Do(e);let g,x;try{if(n.shapeFlag&4){const b=s||o,P=Tt.NODE_ENV!=="production"&&d.__isScriptSetup?new Proxy(b,{get(U,H,W){return xf(`Property '${String(H)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(U,H,W)}}):b;g=nt(a.call(P,b,c,Tt.NODE_ENV!=="production"?bo(f):f,d,h,_)),x=l}else{const b=t;Tt.NODE_ENV,g=nt(b.length>1?b(Tt.NODE_ENV!=="production"?bo(f):f,Tt.NODE_ENV!=="production"?{get attrs(){return bo(l)},slots:i,emit:u}:{attrs:l,slots:i,emit:u}):b(Tt.NODE_ENV!=="production"?bo(f):f,null)),x=t.props?l:bd(l)}}catch(b){Ln.length=0,To(b,e,1),g=Se(At)}let D=g;if(x&&v!==!1){const b=Object.keys(x),{shapeFlag:P}=D;b.length&&P&7&&(r&&b.some(bs)&&(x=wd(x,r)),D=pn(D,x,!1,!0))}return n.dirs&&(D=pn(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&Fs(D,n.transition),g=D,Do(y),g}const bd=e=>{let t;for(const n in e)(n==="class"||n==="style"||co(n))&&((t||(t={}))[n]=e[n]);return t},wd=(e,t)=>{const n={};for(const o in e)(!bs(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function Sd(e,t,n){const{props:o,children:s,component:r}=e,{props:i,children:l,patchFlag:u}=t,a=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&u>=0){if(u&1024)return!0;if(u&16)return o?il(o,i,a):!!i;if(u&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(i[h]!==o[h]&&!Vo(a,h))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:o===i?!1:o?i?il(o,i,a):!0:!!i;return!1}function il(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let s=0;s<o.length;s++){const r=o[s];if(t[r]!==e[r]&&!Vo(n,r))return!0}return!1}function xd({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const ll=e=>e.__isSuspense;function Td(e,t){t&&t.pendingBranch?Y(e)?t.effects.push(...e):t.effects.push(e):If(e)}const Ie=Symbol.for("v-fgt"),Lo=Symbol.for("v-txt"),At=Symbol.for("v-cmt"),Zs=Symbol.for("v-stc"),Ln=[];let $e=null;function Re(e=!1){Ln.push($e=e?null:[])}function Od(){Ln.pop(),$e=Ln[Ln.length-1]||null}let Mn=1;function ul(e,t=!1){Mn+=e,e<0&&$e&&t&&($e.hasOnce=!0)}function al(e){return e.dynamicChildren=Mn>0?$e||on:null,Od(),Mn>0&&$e&&$e.push(e),e}function Ct(e,t,n,o,s,r){return al(ie(e,t,n,o,s,r,!0))}function dn(e,t,n,o,s){return al(Se(e,t,n,o,s,!0))}function $n(e){return e?e.__v_isVNode===!0:!1}function Un(e,t){return e.type===t.type&&e.key===t.key}const cl=({key:e})=>e??null,Mo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?me(e)||ve(e)||K(e)?{i:ye,r:e,k:t,f:!!n}:e:null);function ie(e,t=null,n=null,o=0,s=null,r=e===Ie?0:1,i=!1,l=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&cl(t),ref:t&&Mo(t),scopeId:Co,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ye};return l?(Js(u,n),r&128&&e.normalize(u)):n&&(u.shapeFlag|=me(n)?8:16),Mn>0&&!i&&$e&&(u.patchFlag>0||r&6)&&u.patchFlag!==32&&$e.push(u),u}const Se=Ad;function Ad(e,t=null,n=null,o=0,s=null,r=!1){if((!e||e===qf)&&(e=At),$n(e)){const l=pn(e,t,!0);return n&&Js(l,n),Mn>0&&!r&&$e&&(l.shapeFlag&6?$e[$e.indexOf(e)]=l:$e.push(l)),l.patchFlag=-2,l}if($d(e)&&(e=e.__vccOpts),t){t=fl(t);let{class:l,style:u}=t;l&&!me(l)&&(t.class=ht(l)),de(u)&&(Ls(u)&&!Y(u)&&(u=be({},u)),t.style=Ne(u))}const i=me(e)?1:ll(e)?128:Lf(e)?64:de(e)?4:K(e)?2:0;return ie(e,t,n,o,s,i,r,!0)}function fl(e){return e?Ls(e)||Wi(e)?be({},e):e:null}function pn(e,t,n=!1,o=!1){const{props:s,ref:r,patchFlag:i,children:l,transition:u}=e,a=t?dl(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&cl(a),ref:t&&t.ref?n&&r?Y(r)?r.concat(Mo(t)):[r,Mo(t)]:Mo(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pn(e.ssContent),ssFallback:e.ssFallback&&pn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&o&&Fs(c,u.clone(c)),c}function Cd(e=" ",t=0){return Se(Lo,null,e,t)}function $o(e="",t=!1){return t?(Re(),dn(At,null,e)):Se(At,null,e)}function nt(e){return e==null||typeof e=="boolean"?Se(At):Y(e)?Se(Ie,null,e.slice()):$n(e)?Dt(e):Se(Lo,null,String(e))}function Dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pn(e)}function Js(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(Y(t))n=16;else if(typeof t=="object")if(o&65){const s=t.default;s&&(s._c&&(s._d=!1),Js(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Wi(t)?t._ctx=ye:s===3&&ye&&(ye.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:ye},n=32):(t=String(t),o&64?(n=16,t=[Cd(t)]):n=8);e.children=t,e.shapeFlag|=n}function dl(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const s in o)if(s==="class")t.class!==o.class&&(t.class=ht([t.class,o.class]));else if(s==="style")t.style=Ne([t.style,o.style]);else if(co(s)){const r=t[s],i=o[s];i&&r!==i&&!(Y(r)&&r.includes(i))&&(t[s]=r?[].concat(r,i):i)}else s!==""&&(t[s]=o[s])}return t}function ot(e,t,n,o=null){Qe(e,t,7,[n,o])}const Dd=Hi();let Pd=0;function Id(e,t,n){const o=e.type,s=(t?t.appContext:e.appContext)||Dd,r={uid:Pd++,vnode:e,type:o,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Hc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Yi(o,s),emitsOptions:sl(o,s),emit:null,emitted:null,propsDefaults:le,inheritAttrs:o.inheritAttrs,ctx:le,data:le,props:le,attrs:le,slots:le,refs:le,setupState:le,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Ed.bind(null,r),e.ce&&e.ce(r),r}let ge=null;const Qs=()=>ge||ye;let Uo,er;{const e=_o(),t=(n,o)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(o),r=>{s.length>1?s.forEach(i=>i(r)):s[0](r)}};Uo=t("__VUE_INSTANCE_SETTERS__",n=>ge=n),er=t("__VUE_SSR_SETTERS__",n=>Bn=n)}const Fn=e=>{const t=ge;return Uo(e),e.scope.on(),()=>{e.scope.off(),Uo(t)}},pl=()=>{ge&&ge.scope.off(),Uo(null)};function hl(e){return e.vnode.shapeFlag&4}let Bn=!1;function Rd(e,t=!1,n=!1){t&&er(t);const{props:o,children:s}=e.vnode,r=hl(e);id(e,o,r,t),cd(e,s,n);const i=r?kd(e,t):void 0;return t&&er(!1),i}function kd(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zf);const{setup:o}=n;if(o){_t();const s=e.setupContext=o.length>1?Vd(e):null,r=Fn(e),i=ln(o,e,0,[e.props,s]),l=Qr(i);if(mt(),r(),(l||e.sp)&&!fn(e)&&Pi(e),l){if(i.then(pl,pl),t)return i.then(u=>{_l(e,u)}).catch(u=>{To(u,e,0)});e.asyncDep=i}else _l(e,i)}else ml(e)}function _l(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.setupState=Si(t)),ml(e)}function ml(e,t,n){const o=e.type;e.render||(e.render=o.render||Ke);{const s=Fn(e);_t();try{Jf(e)}finally{mt(),s()}}}const Nd={get(e,t){return we(e,"get",""),e[t]}};function Vd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Nd),slots:e.slots,emit:e.emit,expose:t}}function Fo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Si(ff(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in kn)return kn[n](e)},has(t,n){return n in t||n in kn}})):e.proxy}const Ld=/(?:^|[-_])(\w)/g,Md=e=>e.replace(Ld,t=>t.toUpperCase()).replace(/[-_]/g,"");function gl(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function vl(e,t,n=!1){let o=gl(t);if(!o&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(o=s[1])}if(!o&&e&&e.parent){const s=r=>{for(const i in r)if(r[i]===t)return i};o=s(e.components||e.parent.type.components)||s(e.appContext.components)}return o?Md(o):n?"App":"Anonymous"}function $d(e){return K(e)&&"__vccOpts"in e}const _e=(e,t)=>bf(e,t,Bn);function Ud(e,t,n){const o=arguments.length;return o===2?de(t)&&!Y(t)?$n(t)?Se(e,null,[t]):Se(e,t):Se(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&$n(n)&&(n=[n]),Se(e,t,n))}const Fd="3.5.13";let tr;const yl=typeof window<"u"&&window.trustedTypes;if(yl)try{tr=yl.createPolicy("vue",{createHTML:e=>e})}catch{}const El=tr?e=>tr.createHTML(e):e=>e,Bd="http://www.w3.org/2000/svg",Hd="http://www.w3.org/1998/Math/MathML",bt=typeof document<"u"?document:null,bl=bt&&bt.createElement("template"),zd={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const s=t==="svg"?bt.createElementNS(Bd,e):t==="mathml"?bt.createElementNS(Hd,e):n?bt.createElement(e,{is:n}):bt.createElement(e);return e==="select"&&o&&o.multiple!=null&&s.setAttribute("multiple",o.multiple),s},createText:e=>bt.createTextNode(e),createComment:e=>bt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>bt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,s,r){const i=n?n.previousSibling:t.lastChild;if(s&&(s===r||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===r||!(s=s.nextSibling)););else{bl.innerHTML=El(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const l=bl.content;if(o==="svg"||o==="mathml"){const u=l.firstChild;for(;u.firstChild;)l.appendChild(u.firstChild);l.removeChild(u)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},jd=Symbol("_vtc");function Kd(e,t,n){const o=e[jd];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Bo=Symbol("_vod"),wl=Symbol("_vsh"),st={beforeMount(e,{value:t},{transition:n}){e[Bo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Hn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Hn(e,!0),o.enter(e)):o.leave(e,()=>{Hn(e,!1)}):Hn(e,t))},beforeUnmount(e,{value:t}){Hn(e,t)}};function Hn(e,t){e.style.display=t?e[Bo]:"none",e[wl]=!t}const Wd=Symbol(""),Gd=/(^|;)\s*display\s*:/;function Yd(e,t,n){const o=e.style,s=me(n);let r=!1;if(n&&!s){if(t)if(me(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Ho(o,l,"")}else for(const i in t)n[i]==null&&Ho(o,i,"");for(const i in n)i==="display"&&(r=!0),Ho(o,i,n[i])}else if(s){if(t!==n){const i=o[Wd];i&&(n+=";"+i),o.cssText=n,r=Gd.test(n)}}else t&&e.removeAttribute("style");Bo in e&&(e[Bo]=r?o.display:"",e[wl]&&(o.display="none"))}const Sl=/\s*!important$/;function Ho(e,t,n){if(Y(n))n.forEach(o=>Ho(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=qd(e,t);Sl.test(n)?e.setProperty(St(o),n.replace(Sl,""),"important"):e[o]=n}}const xl=["Webkit","Moz","ms"],nr={};function qd(e,t){const n=nr[t];if(n)return n;let o=He(t);if(o!=="filter"&&o in e)return nr[t]=o;o=ho(o);for(let s=0;s<xl.length;s++){const r=xl[s]+o;if(r in e)return nr[t]=r}return t}const Tl="http://www.w3.org/1999/xlink";function Ol(e,t,n,o,s,r=Fc(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Tl,t.slice(6,t.length)):e.setAttributeNS(Tl,t,n):n==null||r&&!ni(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Ht(n)?String(n):n)}function Al(e,t,n,o,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?El(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,u=n==null?e.type==="checkbox"?"on":"":String(n);(l!==u||!("_value"in e))&&(e.value=u),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ni(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Xd(e,t,n,o){e.addEventListener(t,n,o)}function Zd(e,t,n,o){e.removeEventListener(t,n,o)}const Cl=Symbol("_vei");function Jd(e,t,n,o,s=null){const r=e[Cl]||(e[Cl]={}),i=r[t];if(o&&i)i.value=o;else{const[l,u]=Qd(t);if(o){const a=r[t]=np(o,s);Xd(e,l,a,u)}else i&&(Zd(e,l,i,u),r[t]=void 0)}}const Dl=/(?:Once|Passive|Capture)$/;function Qd(e){let t;if(Dl.test(e)){t={};let o;for(;o=e.match(Dl);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let or=0;const ep=Promise.resolve(),tp=()=>or||(ep.then(()=>or=0),or=Date.now());function np(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Qe(op(o,n.value),t,5,[o])};return n.value=e,n.attached=tp(),n}function op(e,t){if(Y(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>s=>!s._stopped&&o&&o(s))}else return t}const Pl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,sp=(e,t,n,o,s,r)=>{const i=s==="svg";t==="class"?Kd(e,o,i):t==="style"?Yd(e,n,o):co(t)?bs(t)||Jd(e,t,n,o,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):rp(e,t,o,i))?(Al(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ol(e,t,o,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!me(o))?Al(e,He(t),o,r,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Ol(e,t,o,i))};function rp(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Pl(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Pl(t)&&me(n)?!1:t in e}const ip=["ctrl","shift","alt","meta"],lp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ip.some(n=>e[`${n}Key`]&&!t.includes(n))},Pt=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(s,...r)=>{for(let i=0;i<t.length;i++){const l=lp[t[i]];if(l&&l(s,t))return}return e(s,...r)})},up={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},ap=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=s=>{if(!("key"in s))return;const r=St(s.key);if(t.some(i=>i===r||up[i]===r))return e(s)})},cp=be({patchProp:sp},zd);let Il;function fp(){return Il||(Il=dd(cp))}const dp=(...e)=>{const t=fp().createApp(...e),{mount:n}=t;return t.mount=o=>{const s=hp(o);if(!s)return;const r=t._component;!K(r)&&!r.render&&!r.template&&(r.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,pp(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function pp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function hp(e){return me(e)?document.querySelector(e):e}var _p=Object.create,Rl=Object.defineProperty,mp=Object.getOwnPropertyDescriptor,sr=Object.getOwnPropertyNames,gp=Object.getPrototypeOf,vp=Object.prototype.hasOwnProperty,yp=(e,t)=>function(){return e&&(t=(0,e[sr(e)[0]])(e=0)),t},Ep=(e,t)=>function(){return t||(0,e[sr(e)[0]])((t={exports:{}}).exports,t),t.exports},bp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of sr(t))!vp.call(e,s)&&s!==n&&Rl(e,s,{get:()=>t[s],enumerable:!(o=mp(t,s))||o.enumerable});return e},wp=(e,t,n)=>(n=e!=null?_p(gp(e)):{},bp(Rl(n,"default",{value:e,enumerable:!0}),e)),zn=yp({"../../node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/assets/esm_shims.js"(){}}),Sp=Ep({"../../node_modules/.pnpm/rfdc@1.4.1/node_modules/rfdc/index.js"(e,t){zn(),t.exports=o;function n(r){return r instanceof Buffer?Buffer.from(r):new r.constructor(r.buffer.slice(),r.byteOffset,r.length)}function o(r){if(r=r||{},r.circles)return s(r);const i=new Map;if(i.set(Date,f=>new Date(f)),i.set(Map,(f,h)=>new Map(u(Array.from(f),h))),i.set(Set,(f,h)=>new Set(u(Array.from(f),h))),r.constructorHandlers)for(const f of r.constructorHandlers)i.set(f[0],f[1]);let l=null;return r.proto?c:a;function u(f,h){const d=Object.keys(f),_=new Array(d.length);for(let v=0;v<d.length;v++){const y=d[v],g=f[y];typeof g!="object"||g===null?_[y]=g:g.constructor!==Object&&(l=i.get(g.constructor))?_[y]=l(g,h):ArrayBuffer.isView(g)?_[y]=n(g):_[y]=h(g)}return _}function a(f){if(typeof f!="object"||f===null)return f;if(Array.isArray(f))return u(f,a);if(f.constructor!==Object&&(l=i.get(f.constructor)))return l(f,a);const h={};for(const d in f){if(Object.hasOwnProperty.call(f,d)===!1)continue;const _=f[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,a):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=a(_)}return h}function c(f){if(typeof f!="object"||f===null)return f;if(Array.isArray(f))return u(f,c);if(f.constructor!==Object&&(l=i.get(f.constructor)))return l(f,c);const h={};for(const d in f){const _=f[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,c):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=c(_)}return h}}function s(r){const i=[],l=[],u=new Map;if(u.set(Date,d=>new Date(d)),u.set(Map,(d,_)=>new Map(c(Array.from(d),_))),u.set(Set,(d,_)=>new Set(c(Array.from(d),_))),r.constructorHandlers)for(const d of r.constructorHandlers)u.set(d[0],d[1]);let a=null;return r.proto?h:f;function c(d,_){const v=Object.keys(d),y=new Array(v.length);for(let g=0;g<v.length;g++){const x=v[g],D=d[x];if(typeof D!="object"||D===null)y[x]=D;else if(D.constructor!==Object&&(a=u.get(D.constructor)))y[x]=a(D,_);else if(ArrayBuffer.isView(D))y[x]=n(D);else{const b=i.indexOf(D);b!==-1?y[x]=l[b]:y[x]=_(D)}}return y}function f(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return c(d,f);if(d.constructor!==Object&&(a=u.get(d.constructor)))return a(d,f);const _={};i.push(d),l.push(_);for(const v in d){if(Object.hasOwnProperty.call(d,v)===!1)continue;const y=d[v];if(typeof y!="object"||y===null)_[v]=y;else if(y.constructor!==Object&&(a=u.get(y.constructor)))_[v]=a(y,f);else if(ArrayBuffer.isView(y))_[v]=n(y);else{const g=i.indexOf(y);g!==-1?_[v]=l[g]:_[v]=f(y)}}return i.pop(),l.pop(),_}function h(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return c(d,h);if(d.constructor!==Object&&(a=u.get(d.constructor)))return a(d,h);const _={};i.push(d),l.push(_);for(const v in d){const y=d[v];if(typeof y!="object"||y===null)_[v]=y;else if(y.constructor!==Object&&(a=u.get(y.constructor)))_[v]=a(y,h);else if(ArrayBuffer.isView(y))_[v]=n(y);else{const g=i.indexOf(y);g!==-1?_[v]=l[g]:_[v]=h(y)}}return i.pop(),l.pop(),_}}}});zn(),zn(),zn();var jn=typeof navigator<"u",O=typeof window<"u"?window:typeof globalThis<"u"?globalThis:typeof global<"u"?global:{};typeof O.chrome<"u"&&O.chrome.devtools,jn&&(O.self,O.top);var kl;typeof navigator<"u"&&((kl=navigator.userAgent)==null||kl.toLowerCase().includes("electron"));var xp=typeof window<"u"&&!!window.__NUXT__;zn();var Tp=wp(Sp()),Op=/(?:^|[-_/])(\w)/g,Ap=/-(\w)/g,Cp=/([a-z0-9])([A-Z])/g;function Nl(e,t){return t?t.toUpperCase():""}function Vl(e){return e&&`${e}`.replace(Op,Nl)}function Dp(e){return e&&e.replace(Ap,Nl)}function Pp(e){return e&&e.replace(Cp,(t,n,o)=>`${n}-${o}`).toLowerCase()}function Ip(e,t){let n=e.replace(/^[a-z]:/i,"").replace(/\\/g,"/");n.endsWith(`index${t}`)&&(n=n.replace(`/index${t}`,t));const o=n.lastIndexOf("/"),s=n.substring(o+1);{const r=s.lastIndexOf(t);return s.substring(0,r)}}var Ll=(0,Tp.default)({circles:!0});const Rp={trailing:!0};function It(e,t=25,n={}){if(n={...Rp,...n},!Number.isFinite(t))throw new TypeError("Expected `wait` to be a finite number");let o,s,r=[],i,l;const u=(a,c)=>(i=kp(e,a,c),i.finally(()=>{if(i=null,n.trailing&&l&&!s){const f=u(a,l);return l=null,f}}),i);return function(...a){return i?(n.trailing&&(l=a),i):new Promise(c=>{const f=!s&&n.leading;clearTimeout(s),s=setTimeout(()=>{s=null;const h=n.leading?o:u(this,a);for(const d of r)d(h);r=[]},t),f?(o=u(this,a),c(o)):r.push(c)})}}async function kp(e,t,n){return await e.apply(t,n)}function rr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?rr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}const Np={run:e=>e()},Vp=()=>Np,Ml=typeof console.createTask<"u"?console.createTask:Vp;function Lp(e,t){const n=t.shift(),o=Ml(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function Mp(e,t){const n=t.shift(),o=Ml(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function ir(e,t){for(const n of[...e])n(t)}let $p=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,o={}){if(!t||typeof n!="function")return()=>{};const s=t;let r;for(;this._deprecatedHooks[t];)r=this._deprecatedHooks[t],t=r.to;if(r&&!o.allowDeprecated){let i=r.message;i||(i=`${s} hook has been deprecated`+(r.to?`, please use ${r.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let o,s=(...r)=>(typeof o=="function"&&o(),o=void 0,s=void 0,n(...r));return o=this.hook(t,s),o}removeHook(t,n){if(this._hooks[t]){const o=this._hooks[t].indexOf(n);o!==-1&&this._hooks[t].splice(o,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const o=this._hooks[t]||[];delete this._hooks[t];for(const s of o)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=rr(t),o=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of o.splice(0,o.length))s()}}removeHooks(t){const n=rr(t);for(const o in n)this.removeHook(o,n[o])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(Lp,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(Mp,t,...n)}callHookWith(t,n,...o){const s=this._before||this._after?{name:n,args:o,context:{}}:void 0;this._before&&ir(this._before,s);const r=t(n in this._hooks?[...this._hooks[n]]:[],o);return r instanceof Promise?r.finally(()=>{this._after&&s&&ir(this._after,s)}):(this._after&&s&&ir(this._after,s),r)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}};function $l(){return new $p}var Up=Object.create,Ul=Object.defineProperty,Fp=Object.getOwnPropertyDescriptor,lr=Object.getOwnPropertyNames,Bp=Object.getPrototypeOf,Hp=Object.prototype.hasOwnProperty,zp=(e,t)=>function(){return e&&(t=(0,e[lr(e)[0]])(e=0)),t},Fl=(e,t)=>function(){return t||(0,e[lr(e)[0]])((t={exports:{}}).exports,t),t.exports},jp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of lr(t))!Hp.call(e,s)&&s!==n&&Ul(e,s,{get:()=>t[s],enumerable:!(o=Fp(t,s))||o.enumerable});return e},Kp=(e,t,n)=>(n=e!=null?Up(Bp(e)):{},jp(Ul(n,"default",{value:e,enumerable:!0}),e)),w=zp({"../../node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/assets/esm_shims.js"(){}}),Wp=Fl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/lib/speakingurl.js"(e,t){w(),function(n){var o={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"Ae",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"Oe",Ő:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"Ue",Ű:"U",Ý:"Y",Þ:"TH",ß:"ss",à:"a",á:"a",â:"a",ã:"a",ä:"ae",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"oe",ő:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"ue",ű:"u",ý:"y",þ:"th",ÿ:"y","ẞ":"SS",ا:"a",أ:"a",إ:"i",آ:"aa",ؤ:"u",ئ:"e",ء:"a",ب:"b",ت:"t",ث:"th",ج:"j",ح:"h",خ:"kh",د:"d",ذ:"th",ر:"r",ز:"z",س:"s",ش:"sh",ص:"s",ض:"dh",ط:"t",ظ:"z",ع:"a",غ:"gh",ف:"f",ق:"q",ك:"k",ل:"l",م:"m",ن:"n",ه:"h",و:"w",ي:"y",ى:"a",ة:"h",ﻻ:"la",ﻷ:"laa",ﻹ:"lai",ﻵ:"laa",گ:"g",چ:"ch",پ:"p",ژ:"zh",ک:"k",ی:"y","َ":"a","ً":"an","ِ":"e","ٍ":"en","ُ":"u","ٌ":"on","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9",က:"k",ခ:"kh",ဂ:"g",ဃ:"ga",င:"ng",စ:"s",ဆ:"sa",ဇ:"z","စျ":"za",ည:"ny",ဋ:"t",ဌ:"ta",ဍ:"d",ဎ:"da",ဏ:"na",တ:"t",ထ:"ta",ဒ:"d",ဓ:"da",န:"n",ပ:"p",ဖ:"pa",ဗ:"b",ဘ:"ba",မ:"m",ယ:"y",ရ:"ya",လ:"l",ဝ:"w",သ:"th",ဟ:"h",ဠ:"la",အ:"a","ြ":"y","ျ":"ya","ွ":"w","ြွ":"yw","ျွ":"ywa","ှ":"h",ဧ:"e","၏":"-e",ဣ:"i",ဤ:"-i",ဉ:"u",ဦ:"-u",ဩ:"aw","သြော":"aw",ဪ:"aw","၀":"0","၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","္":"","့":"","း":"",č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z",ހ:"h",ށ:"sh",ނ:"n",ރ:"r",ބ:"b",ޅ:"lh",ކ:"k",އ:"a",ވ:"v",މ:"m",ފ:"f",ދ:"dh",ތ:"th",ލ:"l",ގ:"g",ޏ:"gn",ސ:"s",ޑ:"d",ޒ:"z",ޓ:"t",ޔ:"y",ޕ:"p",ޖ:"j",ޗ:"ch",ޘ:"tt",ޙ:"hh",ޚ:"kh",ޛ:"th",ޜ:"z",ޝ:"sh",ޞ:"s",ޟ:"d",ޠ:"t",ޡ:"z",ޢ:"a",ޣ:"gh",ޤ:"q",ޥ:"w","ަ":"a","ާ":"aa","ި":"i","ީ":"ee","ު":"u","ޫ":"oo","ެ":"e","ޭ":"ey","ޮ":"o","ޯ":"oa","ް":"",ა:"a",ბ:"b",გ:"g",დ:"d",ე:"e",ვ:"v",ზ:"z",თ:"t",ი:"i",კ:"k",ლ:"l",მ:"m",ნ:"n",ო:"o",პ:"p",ჟ:"zh",რ:"r",ს:"s",ტ:"t",უ:"u",ფ:"p",ქ:"k",ღ:"gh",ყ:"q",შ:"sh",ჩ:"ch",ც:"ts",ძ:"dz",წ:"ts",ჭ:"ch",ხ:"kh",ჯ:"j",ჰ:"h",α:"a",β:"v",γ:"g",δ:"d",ε:"e",ζ:"z",η:"i",θ:"th",ι:"i",κ:"k",λ:"l",μ:"m",ν:"n",ξ:"ks",ο:"o",π:"p",ρ:"r",σ:"s",τ:"t",υ:"y",φ:"f",χ:"x",ψ:"ps",ω:"o",ά:"a",έ:"e",ί:"i",ό:"o",ύ:"y",ή:"i",ώ:"o",ς:"s",ϊ:"i",ΰ:"y",ϋ:"y",ΐ:"i",Α:"A",Β:"B",Γ:"G",Δ:"D",Ε:"E",Ζ:"Z",Η:"I",Θ:"TH",Ι:"I",Κ:"K",Λ:"L",Μ:"M",Ν:"N",Ξ:"KS",Ο:"O",Π:"P",Ρ:"R",Σ:"S",Τ:"T",Υ:"Y",Φ:"F",Χ:"X",Ψ:"PS",Ω:"O",Ά:"A",Έ:"E",Ί:"I",Ό:"O",Ύ:"Y",Ή:"I",Ώ:"O",Ϊ:"I",Ϋ:"Y",ā:"a",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",ū:"u",Ā:"A",Ē:"E",Ģ:"G",Ī:"I",Ķ:"k",Ļ:"L",Ņ:"N",Ū:"U",Ќ:"Kj",ќ:"kj",Љ:"Lj",љ:"lj",Њ:"Nj",њ:"nj",Тс:"Ts",тс:"ts",ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"E",Ł:"L",Ń:"N",Ś:"S",Ź:"Z",Ż:"Z",Є:"Ye",І:"I",Ї:"Yi",Ґ:"G",є:"ye",і:"i",ї:"yi",ґ:"g",ă:"a",Ă:"A",ș:"s",Ș:"S",ț:"t",Ț:"T",ţ:"t",Ţ:"T",а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"yo",ж:"zh",з:"z",и:"i",й:"i",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"kh",ц:"c",ч:"ch",ш:"sh",щ:"sh",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya",А:"A",Б:"B",В:"V",Г:"G",Д:"D",Е:"E",Ё:"Yo",Ж:"Zh",З:"Z",И:"I",Й:"I",К:"K",Л:"L",М:"M",Н:"N",О:"O",П:"P",Р:"R",С:"S",Т:"T",У:"U",Ф:"F",Х:"Kh",Ц:"C",Ч:"Ch",Ш:"Sh",Щ:"Sh",Ъ:"",Ы:"Y",Ь:"",Э:"E",Ю:"Yu",Я:"Ya",ђ:"dj",ј:"j",ћ:"c",џ:"dz",Ђ:"Dj",Ј:"j",Ћ:"C",Џ:"Dz",ľ:"l",ĺ:"l",ŕ:"r",Ľ:"L",Ĺ:"L",Ŕ:"R",ş:"s",Ş:"S",ı:"i",İ:"I",ğ:"g",Ğ:"G",ả:"a",Ả:"A",ẳ:"a",Ẳ:"A",ẩ:"a",Ẩ:"A",đ:"d",Đ:"D",ẹ:"e",Ẹ:"E",ẽ:"e",Ẽ:"E",ẻ:"e",Ẻ:"E",ế:"e",Ế:"E",ề:"e",Ề:"E",ệ:"e",Ệ:"E",ễ:"e",Ễ:"E",ể:"e",Ể:"E",ỏ:"o",ọ:"o",Ọ:"o",ố:"o",Ố:"O",ồ:"o",Ồ:"O",ổ:"o",Ổ:"O",ộ:"o",Ộ:"O",ỗ:"o",Ỗ:"O",ơ:"o",Ơ:"O",ớ:"o",Ớ:"O",ờ:"o",Ờ:"O",ợ:"o",Ợ:"O",ỡ:"o",Ỡ:"O",Ở:"o",ở:"o",ị:"i",Ị:"I",ĩ:"i",Ĩ:"I",ỉ:"i",Ỉ:"i",ủ:"u",Ủ:"U",ụ:"u",Ụ:"U",ũ:"u",Ũ:"U",ư:"u",Ư:"U",ứ:"u",Ứ:"U",ừ:"u",Ừ:"U",ự:"u",Ự:"U",ữ:"u",Ữ:"U",ử:"u",Ử:"ư",ỷ:"y",Ỷ:"y",ỳ:"y",Ỳ:"Y",ỵ:"y",Ỵ:"Y",ỹ:"y",Ỹ:"Y",ạ:"a",Ạ:"A",ấ:"a",Ấ:"A",ầ:"a",Ầ:"A",ậ:"a",Ậ:"A",ẫ:"a",Ẫ:"A",ắ:"a",Ắ:"A",ằ:"a",Ằ:"A",ặ:"a",Ặ:"A",ẵ:"a",Ẵ:"A","⓪":"0","①":"1","②":"2","③":"3","④":"4","⑤":"5","⑥":"6","⑦":"7","⑧":"8","⑨":"9","⑩":"10","⑪":"11","⑫":"12","⑬":"13","⑭":"14","⑮":"15","⑯":"16","⑰":"17","⑱":"18","⑲":"18","⑳":"18","⓵":"1","⓶":"2","⓷":"3","⓸":"4","⓹":"5","⓺":"6","⓻":"7","⓼":"8","⓽":"9","⓾":"10","⓿":"0","⓫":"11","⓬":"12","⓭":"13","⓮":"14","⓯":"15","⓰":"16","⓱":"17","⓲":"18","⓳":"19","⓴":"20","Ⓐ":"A","Ⓑ":"B","Ⓒ":"C","Ⓓ":"D","Ⓔ":"E","Ⓕ":"F","Ⓖ":"G","Ⓗ":"H","Ⓘ":"I","Ⓙ":"J","Ⓚ":"K","Ⓛ":"L","Ⓜ":"M","Ⓝ":"N","Ⓞ":"O","Ⓟ":"P","Ⓠ":"Q","Ⓡ":"R","Ⓢ":"S","Ⓣ":"T","Ⓤ":"U","Ⓥ":"V","Ⓦ":"W","Ⓧ":"X","Ⓨ":"Y","Ⓩ":"Z","ⓐ":"a","ⓑ":"b","ⓒ":"c","ⓓ":"d","ⓔ":"e","ⓕ":"f","ⓖ":"g","ⓗ":"h","ⓘ":"i","ⓙ":"j","ⓚ":"k","ⓛ":"l","ⓜ":"m","ⓝ":"n","ⓞ":"o","ⓟ":"p","ⓠ":"q","ⓡ":"r","ⓢ":"s","ⓣ":"t","ⓤ":"u","ⓦ":"v","ⓥ":"w","ⓧ":"x","ⓨ":"y","ⓩ":"z","“":'"',"”":'"',"‘":"'","’":"'","∂":"d",ƒ:"f","™":"(TM)","©":"(C)",œ:"oe",Œ:"OE","®":"(R)","†":"+","℠":"(SM)","…":"...","˚":"o",º:"o",ª:"a","•":"*","၊":",","။":".",$:"USD","€":"EUR","₢":"BRN","₣":"FRF","£":"GBP","₤":"ITL","₦":"NGN","₧":"ESP","₩":"KRW","₪":"ILS","₫":"VND","₭":"LAK","₮":"MNT","₯":"GRD","₱":"ARS","₲":"PYG","₳":"ARA","₴":"UAH","₵":"GHS","¢":"cent","¥":"CNY",元:"CNY",円:"YEN","﷼":"IRR","₠":"EWE","฿":"THB","₨":"INR","₹":"INR","₰":"PF","₺":"TRY","؋":"AFN","₼":"AZN",лв:"BGN","៛":"KHR","₡":"CRC","₸":"KZT",ден:"MKD",zł:"PLN","₽":"RUB","₾":"GEL"},s=["်","ް"],r={"ာ":"a","ါ":"a","ေ":"e","ဲ":"e","ိ":"i","ီ":"i","ို":"o","ု":"u","ူ":"u","ေါင်":"aung","ော":"aw","ော်":"aw","ေါ":"aw","ေါ်":"aw","်":"်","က်":"et","ိုက်":"aik","ောက်":"auk","င်":"in","ိုင်":"aing","ောင်":"aung","စ်":"it","ည်":"i","တ်":"at","ိတ်":"eik","ုတ်":"ok","ွတ်":"ut","ေတ်":"it","ဒ်":"d","ိုဒ်":"ok","ုဒ်":"ait","န်":"an","ာန်":"an","ိန်":"ein","ုန်":"on","ွန်":"un","ပ်":"at","ိပ်":"eik","ုပ်":"ok","ွပ်":"ut","န်ုပ်":"nub","မ်":"an","ိမ်":"ein","ုမ်":"on","ွမ်":"un","ယ်":"e","ိုလ်":"ol","ဉ်":"in","ံ":"an","ိံ":"ein","ုံ":"on","ައް":"ah","ަށް":"ah"},i={en:{},az:{ç:"c",ə:"e",ğ:"g",ı:"i",ö:"o",ş:"s",ü:"u",Ç:"C",Ə:"E",Ğ:"G",İ:"I",Ö:"O",Ş:"S",Ü:"U"},cs:{č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z"},fi:{ä:"a",Ä:"A",ö:"o",Ö:"O"},hu:{ä:"a",Ä:"A",ö:"o",Ö:"O",ü:"u",Ü:"U",ű:"u",Ű:"U"},lt:{ą:"a",č:"c",ę:"e",ė:"e",į:"i",š:"s",ų:"u",ū:"u",ž:"z",Ą:"A",Č:"C",Ę:"E",Ė:"E",Į:"I",Š:"S",Ų:"U",Ū:"U"},lv:{ā:"a",č:"c",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",š:"s",ū:"u",ž:"z",Ā:"A",Č:"C",Ē:"E",Ģ:"G",Ī:"i",Ķ:"k",Ļ:"L",Ņ:"N",Š:"S",Ū:"u",Ž:"Z"},pl:{ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ó:"o",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"e",Ł:"L",Ń:"N",Ó:"O",Ś:"S",Ź:"Z",Ż:"Z"},sv:{ä:"a",Ä:"A",ö:"o",Ö:"O"},sk:{ä:"a",Ä:"A"},sr:{љ:"lj",њ:"nj",Љ:"Lj",Њ:"Nj",đ:"dj",Đ:"Dj"},tr:{Ü:"U",Ö:"O",ü:"u",ö:"o"}},l={ar:{"∆":"delta","∞":"la-nihaya","♥":"hob","&":"wa","|":"aw","<":"aqal-men",">":"akbar-men","∑":"majmou","¤":"omla"},az:{},ca:{"∆":"delta","∞":"infinit","♥":"amor","&":"i","|":"o","<":"menys que",">":"mes que","∑":"suma dels","¤":"moneda"},cs:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"nebo","<":"mensi nez",">":"vetsi nez","∑":"soucet","¤":"mena"},de:{"∆":"delta","∞":"unendlich","♥":"Liebe","&":"und","|":"oder","<":"kleiner als",">":"groesser als","∑":"Summe von","¤":"Waehrung"},dv:{"∆":"delta","∞":"kolunulaa","♥":"loabi","&":"aai","|":"noonee","<":"ah vure kuda",">":"ah vure bodu","∑":"jumula","¤":"faisaa"},en:{"∆":"delta","∞":"infinity","♥":"love","&":"and","|":"or","<":"less than",">":"greater than","∑":"sum","¤":"currency"},es:{"∆":"delta","∞":"infinito","♥":"amor","&":"y","|":"u","<":"menos que",">":"mas que","∑":"suma de los","¤":"moneda"},fa:{"∆":"delta","∞":"bi-nahayat","♥":"eshgh","&":"va","|":"ya","<":"kamtar-az",">":"bishtar-az","∑":"majmooe","¤":"vahed"},fi:{"∆":"delta","∞":"aarettomyys","♥":"rakkaus","&":"ja","|":"tai","<":"pienempi kuin",">":"suurempi kuin","∑":"summa","¤":"valuutta"},fr:{"∆":"delta","∞":"infiniment","♥":"Amour","&":"et","|":"ou","<":"moins que",">":"superieure a","∑":"somme des","¤":"monnaie"},ge:{"∆":"delta","∞":"usasruloba","♥":"siqvaruli","&":"da","|":"an","<":"naklebi",">":"meti","∑":"jami","¤":"valuta"},gr:{},hu:{"∆":"delta","∞":"vegtelen","♥":"szerelem","&":"es","|":"vagy","<":"kisebb mint",">":"nagyobb mint","∑":"szumma","¤":"penznem"},it:{"∆":"delta","∞":"infinito","♥":"amore","&":"e","|":"o","<":"minore di",">":"maggiore di","∑":"somma","¤":"moneta"},lt:{"∆":"delta","∞":"begalybe","♥":"meile","&":"ir","|":"ar","<":"maziau nei",">":"daugiau nei","∑":"suma","¤":"valiuta"},lv:{"∆":"delta","∞":"bezgaliba","♥":"milestiba","&":"un","|":"vai","<":"mazak neka",">":"lielaks neka","∑":"summa","¤":"valuta"},my:{"∆":"kwahkhyaet","∞":"asaonasme","♥":"akhyait","&":"nhin","|":"tho","<":"ngethaw",">":"kyithaw","∑":"paungld","¤":"ngwekye"},mk:{},nl:{"∆":"delta","∞":"oneindig","♥":"liefde","&":"en","|":"of","<":"kleiner dan",">":"groter dan","∑":"som","¤":"valuta"},pl:{"∆":"delta","∞":"nieskonczonosc","♥":"milosc","&":"i","|":"lub","<":"mniejsze niz",">":"wieksze niz","∑":"suma","¤":"waluta"},pt:{"∆":"delta","∞":"infinito","♥":"amor","&":"e","|":"ou","<":"menor que",">":"maior que","∑":"soma","¤":"moeda"},ro:{"∆":"delta","∞":"infinit","♥":"dragoste","&":"si","|":"sau","<":"mai mic ca",">":"mai mare ca","∑":"suma","¤":"valuta"},ru:{"∆":"delta","∞":"beskonechno","♥":"lubov","&":"i","|":"ili","<":"menshe",">":"bolshe","∑":"summa","¤":"valjuta"},sk:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"alebo","<":"menej ako",">":"viac ako","∑":"sucet","¤":"mena"},sr:{},tr:{"∆":"delta","∞":"sonsuzluk","♥":"ask","&":"ve","|":"veya","<":"kucuktur",">":"buyuktur","∑":"toplam","¤":"para birimi"},uk:{"∆":"delta","∞":"bezkinechnist","♥":"lubov","&":"i","|":"abo","<":"menshe",">":"bilshe","∑":"suma","¤":"valjuta"},vn:{"∆":"delta","∞":"vo cuc","♥":"yeu","&":"va","|":"hoac","<":"nho hon",">":"lon hon","∑":"tong","¤":"tien te"}},u=[";","?",":","@","&","=","+","$",",","/"].join(""),a=[";","?",":","@","&","=","+","$",","].join(""),c=[".","!","~","*","'","(",")"].join(""),f=function(y,g){var x="-",D="",b="",P=!0,U={},H,W,$,I,R,F,z,Q,Ee,Z,L,X,ue,Ue,xe="";if(typeof y!="string")return"";if(typeof g=="string"&&(x=g),z=l.en,Q=i.en,typeof g=="object"){H=g.maintainCase||!1,U=g.custom&&typeof g.custom=="object"?g.custom:U,$=+g.truncate>1&&g.truncate||!1,I=g.uric||!1,R=g.uricNoSlash||!1,F=g.mark||!1,P=!(g.symbols===!1||g.lang===!1),x=g.separator||x,I&&(xe+=u),R&&(xe+=a),F&&(xe+=c),z=g.lang&&l[g.lang]&&P?l[g.lang]:P?l.en:{},Q=g.lang&&i[g.lang]?i[g.lang]:g.lang===!1||g.lang===!0?{}:i.en,g.titleCase&&typeof g.titleCase.length=="number"&&Array.prototype.toString.call(g.titleCase)?(g.titleCase.forEach(function(he){U[he+""]=he+""}),W=!0):W=!!g.titleCase,g.custom&&typeof g.custom.length=="number"&&Array.prototype.toString.call(g.custom)&&g.custom.forEach(function(he){U[he+""]=he+""}),Object.keys(U).forEach(function(he){var Bt;he.length>1?Bt=new RegExp("\\b"+d(he)+"\\b","gi"):Bt=new RegExp(d(he),"gi"),y=y.replace(Bt,U[he])});for(L in U)xe+=L}for(xe+=x,xe=d(xe),y=y.replace(/(^\s+|\s+$)/g,""),ue=!1,Ue=!1,Z=0,X=y.length;Z<X;Z++)L=y[Z],_(L,U)?ue=!1:Q[L]?(L=ue&&Q[L].match(/[A-Za-z0-9]/)?" "+Q[L]:Q[L],ue=!1):L in o?(Z+1<X&&s.indexOf(y[Z+1])>=0?(b+=L,L=""):Ue===!0?(L=r[b]+o[L],b=""):L=ue&&o[L].match(/[A-Za-z0-9]/)?" "+o[L]:o[L],ue=!1,Ue=!1):L in r?(b+=L,L="",Z===X-1&&(L=r[b]),Ue=!0):z[L]&&!(I&&u.indexOf(L)!==-1)&&!(R&&a.indexOf(L)!==-1)?(L=ue||D.substr(-1).match(/[A-Za-z0-9]/)?x+z[L]:z[L],L+=y[Z+1]!==void 0&&y[Z+1].match(/[A-Za-z0-9]/)?x:"",ue=!0):(Ue===!0?(L=r[b]+L,b="",Ue=!1):ue&&(/[A-Za-z0-9]/.test(L)||D.substr(-1).match(/A-Za-z0-9]/))&&(L=" "+L),ue=!1),D+=L.replace(new RegExp("[^\\w\\s"+xe+"_-]","g"),x);return W&&(D=D.replace(/(\w)(\S*)/g,function(he,Bt,vs){var io=Bt.toUpperCase()+(vs!==null?vs:"");return Object.keys(U).indexOf(io.toLowerCase())<0?io:io.toLowerCase()})),D=D.replace(/\s+/g,x).replace(new RegExp("\\"+x+"+","g"),x).replace(new RegExp("(^\\"+x+"+|\\"+x+"+$)","g"),""),$&&D.length>$&&(Ee=D.charAt($)===x,D=D.slice(0,$),Ee||(D=D.slice(0,D.lastIndexOf(x)))),!H&&!W&&(D=D.toLowerCase()),D},h=function(y){return function(x){return f(x,y)}},d=function(y){return y.replace(/[-\\^$*+?.()|[\]{}\/]/g,"\\$&")},_=function(v,y){for(var g in y)if(y[g]===v)return!0};if(typeof t<"u"&&t.exports)t.exports=f,t.exports.createSlug=h;else if(typeof define<"u"&&define.amd)define([],function(){return f});else try{if(n.getSlug||n.createSlug)throw"speakingurl: globals exists /(getSlug|createSlug)/";n.getSlug=f,n.createSlug=h}catch{}}(e)}}),Gp=Fl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/index.js"(e,t){w(),t.exports=Wp()}});w(),w(),w();function Yp(e){if(O.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__){e();return}Object.defineProperty(O,"__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__",{set(t){t&&e()},configurable:!0})}w(),w(),w(),w(),w();function qp(e){var t;const n=e.name||e._componentTag||e.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__||e.__name;return n==="index"&&((t=e.__file)!=null&&t.endsWith("index.vue"))?"":n}function Xp(e){const t=e.__file;if(t)return Vl(Ip(t,".vue"))}function Bl(e,t){return e.type.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__=t,t}function qe(e){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD__)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(e.root)return e.appContext.app.__VUE_DEVTOOLS_NEXT_APP_RECORD__}async function ur(e){const{app:t,uid:n,instance:o}=e;try{if(o.__VUE_DEVTOOLS_NEXT_UID__)return o.__VUE_DEVTOOLS_NEXT_UID__;const s=await qe(t);if(!s)return null;const r=s.rootInstance===o;return`${s.id}:${r?"root":n}`}catch{}}function ar(e){var t,n;const o=(t=e.subTree)==null?void 0:t.type,s=qe(e);return s?((n=s==null?void 0:s.types)==null?void 0:n.Fragment)===o:!1}function cr(e){return e._isBeingDestroyed||e.isUnmounted}function rt(e){var t,n,o;const s=qp((e==null?void 0:e.type)||{});if(s)return s;if((e==null?void 0:e.root)===e)return"Root";for(const i in(n=(t=e.parent)==null?void 0:t.type)==null?void 0:n.components)if(e.parent.type.components[i]===(e==null?void 0:e.type))return Bl(e,i);for(const i in(o=e.appContext)==null?void 0:o.components)if(e.appContext.components[i]===(e==null?void 0:e.type))return Bl(e,i);const r=Xp((e==null?void 0:e.type)||{});return r||"Anonymous Component"}function fr(e){var t,n,o;const s=(o=(n=(t=e==null?void 0:e.appContext)==null?void 0:t.app)==null?void 0:n.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__)!=null?o:0,r=e===(e==null?void 0:e.root)?"root":e.uid;return`${s}:${r}`}function Zp(e){return e==null?"":typeof e=="number"?e:typeof e=="string"?`'${e}'`:Array.isArray(e)?"Array":"Object"}function Rt(e){try{return e()}catch(t){return t}}function hn(e,t){return t=t||`${e.id}:root`,e.instanceMap.get(t)||e.instanceMap.get(":root")}function dr(e,t,n=!1){return n||typeof e=="object"&&e!==null?t in e:!1}function Jp(){const e={top:0,bottom:0,left:0,right:0,get width(){return e.right-e.left},get height(){return e.bottom-e.top}};return e}var zo;function Qp(e){return zo||(zo=document.createRange()),zo.selectNode(e),zo.getBoundingClientRect()}function eh(e){const t=Jp();if(!e.children)return t;for(let n=0,o=e.children.length;n<o;n++){const s=e.children[n];let r;if(s.component)r=qt(s.component);else if(s.el){const i=s.el;i.nodeType===1||i.getBoundingClientRect?r=i.getBoundingClientRect():i.nodeType===3&&i.data.trim()&&(r=Qp(i))}r&&th(t,r)}return t}function th(e,t){return(!e.top||t.top<e.top)&&(e.top=t.top),(!e.bottom||t.bottom>e.bottom)&&(e.bottom=t.bottom),(!e.left||t.left<e.left)&&(e.left=t.left),(!e.right||t.right>e.right)&&(e.right=t.right),e}var Hl={top:0,left:0,right:0,bottom:0,width:0,height:0};function qt(e){const t=e.subTree.el;return typeof window>"u"?Hl:ar(e)?eh(e.subTree):(t==null?void 0:t.nodeType)===1?t==null?void 0:t.getBoundingClientRect():e.subTree.component?qt(e.subTree.component):Hl}w();function _n(e){return ar(e)?nh(e.subTree):e.subTree?[e.subTree.el]:[]}function nh(e){if(!e.children)return[];const t=[];return e.children.forEach(n=>{n.component?t.push(..._n(n.component)):n!=null&&n.el&&t.push(n.el)}),t}var zl="__vue-devtools-component-inspector__",jl="__vue-devtools-component-inspector__card__",Kl="__vue-devtools-component-inspector__name__",Wl="__vue-devtools-component-inspector__indicator__",Gl={display:"block",zIndex:2147483640,position:"fixed",backgroundColor:"#42b88325",border:"1px solid #42b88350",borderRadius:"5px",transition:"all 0.1s ease-in",pointerEvents:"none"},oh={fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 8px",borderRadius:"4px",textAlign:"left",position:"absolute",left:0,color:"#e9e9e9",fontSize:"14px",fontWeight:600,lineHeight:"24px",backgroundColor:"#42b883",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},sh={display:"inline-block",fontWeight:400,fontStyle:"normal",fontSize:"12px",opacity:.7};function mn(){return document.getElementById(zl)}function rh(){return document.getElementById(jl)}function ih(){return document.getElementById(Wl)}function lh(){return document.getElementById(Kl)}function pr(e){return{left:`${Math.round(e.left*100)/100}px`,top:`${Math.round(e.top*100)/100}px`,width:`${Math.round(e.width*100)/100}px`,height:`${Math.round(e.height*100)/100}px`}}function hr(e){var t;const n=document.createElement("div");n.id=(t=e.elementId)!=null?t:zl,Object.assign(n.style,{...Gl,...pr(e.bounds),...e.style});const o=document.createElement("span");o.id=jl,Object.assign(o.style,{...oh,top:e.bounds.top<35?0:"-35px"});const s=document.createElement("span");s.id=Kl,s.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`;const r=document.createElement("i");return r.id=Wl,r.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`,Object.assign(r.style,sh),o.appendChild(s),o.appendChild(r),n.appendChild(o),document.body.appendChild(n),n}function _r(e){const t=mn(),n=rh(),o=lh(),s=ih();t&&(Object.assign(t.style,{...Gl,...pr(e.bounds)}),Object.assign(n.style,{top:e.bounds.top<35?0:"-35px"}),o.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`,s.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`)}function uh(e){const t=qt(e);if(!t.width&&!t.height)return;const n=rt(e);mn()?_r({bounds:t,name:n}):hr({bounds:t,name:n})}function Yl(){const e=mn();e&&(e.style.display="none")}var mr=null;function gr(e){const t=e.target;if(t){const n=t.__vueParentComponent;if(n&&(mr=n,n.vnode.el)){const s=qt(n),r=rt(n);mn()?_r({bounds:s,name:r}):hr({bounds:s,name:r})}}}function ah(e,t){if(e.preventDefault(),e.stopPropagation(),mr){const n=fr(mr);t(n)}}var jo=null;function ch(){Yl(),window.removeEventListener("mouseover",gr),window.removeEventListener("click",jo,!0),jo=null}function fh(){return window.addEventListener("mouseover",gr),new Promise(e=>{function t(n){n.preventDefault(),n.stopPropagation(),ah(n,o=>{window.removeEventListener("click",t,!0),jo=null,window.removeEventListener("mouseover",gr);const s=mn();s&&(s.style.display="none"),e(JSON.stringify({id:o}))})}jo=t,window.addEventListener("click",t,!0)})}function dh(e){const t=hn(se.value,e.id);if(t){const[n]=_n(t);if(typeof n.scrollIntoView=="function")n.scrollIntoView({behavior:"smooth"});else{const o=qt(t),s=document.createElement("div"),r={...pr(o),position:"absolute"};Object.assign(s.style,r),document.body.appendChild(s),s.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{document.body.removeChild(s)},2e3)}setTimeout(()=>{const o=qt(t);if(o.width||o.height){const s=rt(t),r=mn();r?_r({...e,name:s,bounds:o}):hr({...e,name:s,bounds:o}),setTimeout(()=>{r&&(r.style.display="none")},1500)}},1200)}}w();var ql,Xl;(Xl=(ql=O).__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__)!=null||(ql.__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__=!0);function ph(e){let t=0;const n=setInterval(()=>{O.__VUE_INSPECTOR__&&(clearInterval(n),t+=30,e()),t>=5e3&&clearInterval(n)},30)}function hh(){const e=O.__VUE_INSPECTOR__,t=e.openInEditor;e.openInEditor=async(...n)=>{e.disable(),t(...n)}}function _h(){return new Promise(e=>{function t(){hh(),e(O.__VUE_INSPECTOR__)}O.__VUE_INSPECTOR__?t():ph(()=>{t()})})}w(),w();function mh(e){return!!(e&&e.__v_isReadonly)}function Zl(e){return mh(e)?Zl(e.__v_raw):!!(e&&e.__v_isReactive)}function vr(e){return!!(e&&e.__v_isRef===!0)}function Kn(e){const t=e&&e.__v_raw;return t?Kn(t):e}var Jl=class{constructor(){this.refEditor=new gh}set(e,t,n,o){const s=Array.isArray(t)?t:t.split(".");for(;s.length>1;){const l=s.shift();e instanceof Map?e=e.get(l):e instanceof Set?e=Array.from(e.values())[l]:e=e[l],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}const r=s[0],i=this.refEditor.get(e)[r];o?o(e,r,n):this.refEditor.isRef(i)?this.refEditor.set(i,n):e[r]=n}get(e,t){const n=Array.isArray(t)?t:t.split(".");for(let o=0;o<n.length;o++)if(e instanceof Map?e=e.get(n[o]):e=e[n[o]],this.refEditor.isRef(e)&&(e=this.refEditor.get(e)),!e)return;return e}has(e,t,n=!1){if(typeof e>"u")return!1;const o=Array.isArray(t)?t.slice():t.split("."),s=n?2:1;for(;e&&o.length>s;){const r=o.shift();e=e[r],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}return e!=null&&Object.prototype.hasOwnProperty.call(e,o[0])}createDefaultSetCallback(e){return(t,n,o)=>{if((e.remove||e.newKey)&&(Array.isArray(t)?t.splice(n,1):Kn(t)instanceof Map?t.delete(n):Kn(t)instanceof Set?t.delete(Array.from(t.values())[n]):Reflect.deleteProperty(t,n)),!e.remove){const s=t[e.newKey||n];this.refEditor.isRef(s)?this.refEditor.set(s,o):Kn(t)instanceof Map?t.set(e.newKey||n,o):Kn(t)instanceof Set?t.add(o):t[e.newKey||n]=o}}}},gh=class{set(e,t){if(vr(e))e.value=t;else{if(e instanceof Set&&Array.isArray(t)){e.clear(),t.forEach(s=>e.add(s));return}const n=Object.keys(t);if(e instanceof Map){const s=new Set(e.keys());n.forEach(r=>{e.set(r,Reflect.get(t,r)),s.delete(r)}),s.forEach(r=>e.delete(r));return}const o=new Set(Object.keys(e));n.forEach(s=>{Reflect.set(e,s,Reflect.get(t,s)),o.delete(s)}),o.forEach(s=>Reflect.deleteProperty(e,s))}}get(e){return vr(e)?e.value:e}isRef(e){return vr(e)||Zl(e)}};async function vh(e,t){const{path:n,nodeId:o,state:s,type:r}=e,i=hn(se.value,o);if(!i)return;const l=n.slice();let u;Object.keys(i.props).includes(n[0])?u=i.props:i.devtoolsRawSetupState&&Object.keys(i.devtoolsRawSetupState).includes(n[0])?u=i.devtoolsRawSetupState:i.data&&Object.keys(i.data).includes(n[0])?u=i.data:u=i.proxy,u&&l&&(s.type,t.set(u,l,s.value,t.createDefaultSetCallback(s)))}var yh=new Jl;async function Eh(e){vh(e,yh)}w(),w(),w();var bh="__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS_STATE__";function wh(){if(!jn||typeof localStorage>"u"||localStorage===null)return{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""};const e=localStorage.getItem(bh);return e?JSON.parse(e):{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""}}w(),w(),w();var Ql,eu;(eu=(Ql=O).__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS)!=null||(Ql.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS=[]);var Sh=new Proxy(O.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS,{get(e,t,n){return Reflect.get(e,t,n)}});function xh(e,t){q.timelineLayersState[t.id]=!1,Sh.push({...e,descriptorId:t.id,appRecord:qe(t.app)})}var tu,nu;(nu=(tu=O).__VUE_DEVTOOLS_KIT_INSPECTOR__)!=null||(tu.__VUE_DEVTOOLS_KIT_INSPECTOR__=[]);var yr=new Proxy(O.__VUE_DEVTOOLS_KIT_INSPECTOR__,{get(e,t,n){return Reflect.get(e,t,n)}}),ou=It(()=>{Xe.hooks.callHook("sendInspectorToClient",su())});function Th(e,t){var n,o;yr.push({options:e,descriptor:t,treeFilterPlaceholder:(n=e.treeFilterPlaceholder)!=null?n:"Search tree...",stateFilterPlaceholder:(o=e.stateFilterPlaceholder)!=null?o:"Search state...",treeFilter:"",selectedNodeId:"",appRecord:qe(t.app)}),ou()}function su(){return yr.filter(e=>e.descriptor.app===se.value.app).filter(e=>e.descriptor.id!=="components").map(e=>{var t;const n=e.descriptor,o=e.options;return{id:o.id,label:o.label,logo:n.logo,icon:`custom-ic-baseline-${(t=o==null?void 0:o.icon)==null?void 0:t.replace(/_/g,"-")}`,packageName:n.packageName,homepage:n.homepage,pluginId:n.id}})}function Ko(e,t){return yr.find(n=>n.options.id===e&&(t?n.descriptor.app===t:!0))}function Oh(){const e=$l();e.hook("addInspector",({inspector:o,plugin:s})=>{Th(o,s.descriptor)});const t=It(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=Ko(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,filter:(i==null?void 0:i.treeFilter)||"",rootNodes:[]};await new Promise(u=>{e.callHookWith(async a=>{await Promise.all(a.map(c=>c(l))),u()},"getInspectorTree")}),e.callHookWith(async u=>{await Promise.all(u.map(a=>a({inspectorId:o,rootNodes:l.rootNodes})))},"sendInspectorTreeToClient")},120);e.hook("sendInspectorTree",t);const n=It(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=Ko(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,nodeId:(i==null?void 0:i.selectedNodeId)||"",state:null},u={currentTab:`custom-inspector:${o}`};l.nodeId&&await new Promise(a=>{e.callHookWith(async c=>{await Promise.all(c.map(f=>f(l,u))),a()},"getInspectorState")}),e.callHookWith(async a=>{await Promise.all(a.map(c=>c({inspectorId:o,nodeId:l.nodeId,state:l.state})))},"sendInspectorStateToClient")},120);return e.hook("sendInspectorState",n),e.hook("customInspectorSelectNode",({inspectorId:o,nodeId:s,plugin:r})=>{const i=Ko(o,r.descriptor.app);i&&(i.selectedNodeId=s)}),e.hook("timelineLayerAdded",({options:o,plugin:s})=>{xh(o,s.descriptor)}),e.hook("timelineEventAdded",({options:o,plugin:s})=>{var r;const i=["performance","component-event","keyboard","mouse"];q.highPerfModeEnabled||!((r=q.timelineLayersState)!=null&&r[s.descriptor.id])&&!i.includes(o.layerId)||e.callHookWith(async l=>{await Promise.all(l.map(u=>u(o)))},"sendTimelineEventToClient")}),e.hook("getComponentInstances",async({app:o})=>{const s=o.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(!s)return null;const r=s.id.toString();return[...s.instanceMap].filter(([l])=>l.split(":")[0]===r).map(([,l])=>l)}),e.hook("getComponentBounds",async({instance:o})=>qt(o)),e.hook("getComponentName",({instance:o})=>rt(o)),e.hook("componentHighlight",({uid:o})=>{const s=se.value.instanceMap.get(o);s&&uh(s)}),e.hook("componentUnhighlight",()=>{Yl()}),e}var ru,iu;(iu=(ru=O).__VUE_DEVTOOLS_KIT_APP_RECORDS__)!=null||(ru.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[]);var lu,uu;(uu=(lu=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__)!=null||(lu.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__={});var au,cu;(cu=(au=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__)!=null||(au.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__="");var fu,du;(du=(fu=O).__VUE_DEVTOOLS_KIT_CUSTOM_TABS__)!=null||(fu.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__=[]);var pu,hu;(hu=(pu=O).__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__)!=null||(pu.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__=[]);var it="__VUE_DEVTOOLS_KIT_GLOBAL_STATE__";function Ah(){return{connected:!1,clientConnected:!1,vitePluginDetected:!0,appRecords:[],activeAppRecordId:"",tabs:[],commands:[],highPerfModeEnabled:!0,devtoolsClientDetected:{},perfUniqueGroupId:0,timelineLayersState:wh()}}var _u,mu;(mu=(_u=O)[it])!=null||(_u[it]=Ah());var Ch=It(e=>{Xe.hooks.callHook("devtoolsStateUpdated",{state:e})}),Dh=It((e,t)=>{Xe.hooks.callHook("devtoolsConnectedUpdated",{state:e,oldState:t})}),kt=new Proxy(O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_APP_RECORDS__:O.__VUE_DEVTOOLS_KIT_APP_RECORDS__[t]}}),Ph=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[...O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,e]},Ih=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=kt.value.filter(t=>t.app!==e)},se=new Proxy(O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__:t==="id"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__:O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__[t]}});function Er(){Ch({...O[it],appRecords:kt.value,activeAppRecordId:se.id,tabs:O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__,commands:O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__})}function br(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__=e,Er()}function gu(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__=e,Er()}var q=new Proxy(O[it],{get(e,t){return t==="appRecords"?kt:t==="activeAppRecordId"?se.id:t==="tabs"?O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__:t==="commands"?O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__:O[it][t]},deleteProperty(e,t){return delete e[t],!0},set(e,t,n){return{...O[it]},e[t]=n,O[it][t]=n,!0}});function wr(e){const t={...O[it],appRecords:kt.value,activeAppRecordId:se.id};(t.connected!==e.connected&&e.connected||t.clientConnected!==e.clientConnected&&e.clientConnected)&&Dh(O[it],t),Object.assign(O[it],e),Er()}function Rh(e){return new Promise(t=>{q.connected&&(e(),t()),Xe.hooks.hook("devtoolsConnectedUpdated",({state:n})=>{n.connected&&(e(),t())})})}function kh(e={}){var t,n,o;const{file:s,host:r,baseUrl:i=window.location.origin,line:l=0,column:u=0}=e;if(s){if(r==="chrome-extension"){const a=s.replace(/\\/g,"\\\\"),c=(n=(t=window.VUE_DEVTOOLS_CONFIG)==null?void 0:t.openInEditorHost)!=null?n:"/";fetch(`${c}__open-in-editor?file=${encodeURI(s)}`).then(f=>{if(!f.ok){const h=`Opening component ${a} failed`;console.log(`%c${h}`,"color:red")}})}else if(q.vitePluginDetected){const a=(o=O.__VUE_DEVTOOLS_OPEN_IN_EDITOR_BASE_URL__)!=null?o:i;O.__VUE_INSPECTOR__.openInEditor(a,s,l,u)}}}w(),w(),w(),w(),w();var vu,yu;(yu=(vu=O).__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__)!=null||(vu.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__=[]);var Wn=new Proxy(O.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__,{get(e,t,n){return Reflect.get(e,t,n)}});function Nh(e,t){Wn.push([e,t])}function Sr(e){const t={};return Object.keys(e).forEach(n=>{t[n]=e[n].defaultValue}),t}function xr(e){return`__VUE_DEVTOOLS_NEXT_PLUGIN_SETTINGS__${e}__`}function Vh(e){var t,n,o;const s=(n=(t=Wn.find(r=>{var i;return r[0].id===e&&!!((i=r[0])!=null&&i.settings)}))==null?void 0:t[0])!=null?n:null;return(o=s==null?void 0:s.settings)!=null?o:null}function Eu(e,t){var n,o,s;const r=xr(e);if(r){const i=localStorage.getItem(r);if(i)return JSON.parse(i)}if(e){const i=(o=(n=Wn.find(l=>l[0].id===e))==null?void 0:n[0])!=null?o:null;return Sr((s=i==null?void 0:i.settings)!=null?s:{})}return Sr(t)}function bu(e,t){const n=xr(e);localStorage.getItem(n)||localStorage.setItem(n,JSON.stringify(Sr(t)))}function Lh(e,t,n){const o=xr(e),s=localStorage.getItem(o),r=JSON.parse(s||"{}"),i={...r,[t]:n};localStorage.setItem(o,JSON.stringify(i)),Xe.hooks.callHookWith(l=>{l.forEach(u=>u({pluginId:e,key:t,oldValue:r[t],newValue:n,settings:i}))},"setPluginSettings")}w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var wu,Su,pe=(Su=(wu=O).__VUE_DEVTOOLS_HOOK)!=null?Su:wu.__VUE_DEVTOOLS_HOOK=$l(),Mh={vueAppInit(e){pe.hook("app:init",e)},vueAppUnmount(e){pe.hook("app:unmount",e)},vueAppConnected(e){pe.hook("app:connected",e)},componentAdded(e){return pe.hook("component:added",e)},componentEmit(e){return pe.hook("component:emit",e)},componentUpdated(e){return pe.hook("component:updated",e)},componentRemoved(e){return pe.hook("component:removed",e)},setupDevtoolsPlugin(e){pe.hook("devtools-plugin:setup",e)},perfStart(e){return pe.hook("perf:start",e)},perfEnd(e){return pe.hook("perf:end",e)}};function $h(){return{id:"vue-devtools-next",devtoolsVersion:"7.0",enabled:!1,appRecords:[],apps:[],events:new Map,on(e,t){var n;return this.events.has(e)||this.events.set(e,[]),(n=this.events.get(e))==null||n.push(t),()=>this.off(e,t)},once(e,t){const n=(...o)=>{this.off(e,n),t(...o)};return this.on(e,n),[e,n]},off(e,t){if(this.events.has(e)){const n=this.events.get(e),o=n.indexOf(t);o!==-1&&n.splice(o,1)}},emit(e,...t){this.events.has(e)&&this.events.get(e).forEach(n=>n(...t))}}}function Uh(e){e.on("app:init",(t,n,o)=>{var s,r,i;(i=(r=(s=t==null?void 0:t._instance)==null?void 0:s.type)==null?void 0:r.devtools)!=null&&i.hide||pe.callHook("app:init",t,n,o)}),e.on("app:unmount",t=>{pe.callHook("app:unmount",t)}),e.on("component:added",async(t,n,o,s)=>{var r,i,l;(l=(i=(r=t==null?void 0:t._instance)==null?void 0:r.type)==null?void 0:i.devtools)!=null&&l.hide||q.highPerfModeEnabled||!t||typeof n!="number"&&!n||!s||pe.callHook("component:added",t,n,o,s)}),e.on("component:updated",(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||pe.callHook("component:updated",t,n,o,s)}),e.on("component:removed",async(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||pe.callHook("component:removed",t,n,o,s)}),e.on("component:emit",async(t,n,o,s)=>{!t||!n||q.highPerfModeEnabled||pe.callHook("component:emit",t,n,o,s)}),e.on("perf:start",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||pe.callHook("perf:start",t,n,o,s,r)}),e.on("perf:end",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||pe.callHook("perf:end",t,n,o,s,r)}),e.on("devtools-plugin:setup",(t,n,o)=>{(o==null?void 0:o.target)!=="legacy"&&pe.callHook("devtools-plugin:setup",t,n)})}var ze={on:Mh,setupDevToolsPlugin(e,t){return pe.callHook("devtools-plugin:setup",e,t)}},Fh=class{constructor({plugin:e,ctx:t}){this.hooks=t.hooks,this.plugin=e}get on(){return{visitComponentTree:e=>{this.hooks.hook("visitComponentTree",e)},inspectComponent:e=>{this.hooks.hook("inspectComponent",e)},editComponentState:e=>{this.hooks.hook("editComponentState",e)},getInspectorTree:e=>{this.hooks.hook("getInspectorTree",e)},getInspectorState:e=>{this.hooks.hook("getInspectorState",e)},editInspectorState:e=>{this.hooks.hook("editInspectorState",e)},inspectTimelineEvent:e=>{this.hooks.hook("inspectTimelineEvent",e)},timelineCleared:e=>{this.hooks.hook("timelineCleared",e)},setPluginSettings:e=>{this.hooks.hook("setPluginSettings",e)}}}notifyComponentUpdate(e){var t;if(q.highPerfModeEnabled)return;const n=su().find(o=>o.packageName===this.plugin.descriptor.packageName);if(n!=null&&n.id){if(e){const o=[e.appContext.app,e.uid,(t=e.parent)==null?void 0:t.uid,e];pe.callHook("component:updated",...o)}else pe.callHook("component:updated");this.hooks.callHook("sendInspectorState",{inspectorId:n.id,plugin:this.plugin})}}addInspector(e){this.hooks.callHook("addInspector",{inspector:e,plugin:this.plugin}),this.plugin.descriptor.settings&&bu(e.id,this.plugin.descriptor.settings)}sendInspectorTree(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorTree",{inspectorId:e,plugin:this.plugin})}sendInspectorState(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorState",{inspectorId:e,plugin:this.plugin})}selectInspectorNode(e,t){this.hooks.callHook("customInspectorSelectNode",{inspectorId:e,nodeId:t,plugin:this.plugin})}visitComponentTree(e){return this.hooks.callHook("visitComponentTree",e)}now(){return q.highPerfModeEnabled?0:Date.now()}addTimelineLayer(e){this.hooks.callHook("timelineLayerAdded",{options:e,plugin:this.plugin})}addTimelineEvent(e){q.highPerfModeEnabled||this.hooks.callHook("timelineEventAdded",{options:e,plugin:this.plugin})}getSettings(e){return Eu(e??this.plugin.descriptor.id,this.plugin.descriptor.settings)}getComponentInstances(e){return this.hooks.callHook("getComponentInstances",{app:e})}getComponentBounds(e){return this.hooks.callHook("getComponentBounds",{instance:e})}getComponentName(e){return this.hooks.callHook("getComponentName",{instance:e})}highlightElement(e){const t=e.__VUE_DEVTOOLS_NEXT_UID__;return this.hooks.callHook("componentHighlight",{uid:t})}unhighlightElement(){return this.hooks.callHook("componentUnhighlight")}},Bh=Fh;w(),w(),w(),w();var Hh=new Set(["nextTick","defineComponent","defineAsyncComponent","defineCustomElement","ref","computed","reactive","readonly","watchEffect","watchPostEffect","watchSyncEffect","watch","isRef","unref","toRef","toRefs","isProxy","isReactive","isReadonly","shallowRef","triggerRef","customRef","shallowReactive","shallowReadonly","toRaw","markRaw","effectScope","getCurrentScope","onScopeDispose","onMounted","onUpdated","onUnmounted","onBeforeMount","onBeforeUpdate","onBeforeUnmount","onErrorCaptured","onRenderTracked","onRenderTriggered","onActivated","onDeactivated","onServerPrefetch","provide","inject","h","mergeProps","cloneVNode","isVNode","resolveComponent","resolveDirective","withDirectives","withModifiers"]),zh=/^(?:function|class) (\w+)/,jh="__vue_devtool_undefined__",Kh="__vue_devtool_infinity__",Wh="__vue_devtool_negative_infinity__",Gh="__vue_devtool_nan__";w(),w();function xu(e){return!!e.__v_isRef}function Yh(e){return xu(e)&&!!e.effect}function qh(e){return!!e.__v_isReactive}function Xh(e){return!!e.__v_isReadonly}var Zh={[jh]:"undefined",[Gh]:"NaN",[Kh]:"Infinity",[Wh]:"-Infinity"};Object.entries(Zh).reduce((e,[t,n])=>(e[n]=t,e),{});function Tu(e){if(Array.isArray(e))return e.map(n=>Tu(n)).join(" or ");if(e==null)return"null";const t=e.toString().match(zh);return typeof e=="function"&&t&&t[1]||"any"}function Jh(e){try{return{ref:xu(e),computed:Yh(e),reactive:qh(e),readonly:Xh(e)}}catch{return{ref:!1,computed:!1,reactive:!1,readonly:!1}}}function Qh(e){return e!=null&&e.__v_raw?e.__v_raw:e}function Wo(e,t,n){if(typeof t=="function"&&(t=t.options),!t)return e;const{mixins:o,extends:s}=t;s&&Wo(e,s),o&&o.forEach(r=>Wo(e,r));for(const r of["computed","inject"])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]?Object.assign(e[r],t[r]):e[r]=t[r]);return e}function e_(e){const t=e==null?void 0:e.type;if(!t)return{};const{mixins:n,extends:o}=t,s=e.appContext.mixins;if(!s.length&&!n&&!o)return t;const r={};return s.forEach(i=>Wo(r,i)),Wo(r,t),r}function t_(e){var t;const n=[],o=(t=e==null?void 0:e.type)==null?void 0:t.props;for(const s in e==null?void 0:e.props){const r=o?o[s]:null,i=Dp(s);n.push({type:"props",key:i,value:Rt(()=>e.props[s]),editable:!0,meta:r?{type:r.type?Tu(r.type):"any",required:!!r.required,...r.default?{default:r.default.toString()}:{}}:{type:"invalid"}})}return n}function n_(e){const t=e.type,n=t==null?void 0:t.props,o=t.vuex&&t.vuex.getters,s=t.computed,r={...e.data,...e.renderContext};return Object.keys(r).filter(i=>!(n&&i in n)&&!(o&&i in o)&&!(s&&i in s)).map(i=>({key:i,type:"data",value:Rt(()=>r[i]),editable:!0}))}function o_(e){const t=e.computed?"computed":e.ref?"ref":e.reactive?"reactive":null,n=t?`${t.charAt(0).toUpperCase()}${t.slice(1)}`:null;return{stateType:t,stateTypeName:n}}function s_(e){const t=e.devtoolsRawSetupState||{};return Object.keys(e.setupState).filter(n=>!Hh.has(n)&&n.split(/(?=[A-Z])/)[0]!=="use").map(n=>{var o,s,r,i;const l=Rt(()=>Qh(e.setupState[n])),u=l instanceof Error,a=t[n];let c,f=u||typeof l=="function"||dr(l,"render")&&typeof l.render=="function"||dr(l,"__asyncLoader")&&typeof l.__asyncLoader=="function"||typeof l=="object"&&l&&("setup"in l||"props"in l)||/^v[A-Z]/.test(n);if(a&&!u){const d=Jh(a),{stateType:_,stateTypeName:v}=o_(d),y=d.ref||d.computed||d.reactive,g=dr(a,"effect")?((s=(o=a.effect)==null?void 0:o.raw)==null?void 0:s.toString())||((i=(r=a.effect)==null?void 0:r.fn)==null?void 0:i.toString()):null;_&&(f=!1),c={..._?{stateType:_,stateTypeName:v}:{},...g?{raw:g}:{},editable:y&&!d.readonly}}return{key:n,value:l,type:f?"setup (other)":"setup",...c}})}function r_(e,t){const n=t,o=[],s=n.computed||{};for(const r in s){const i=s[r],l=typeof i=="function"&&i.vuex?"vuex bindings":"computed";o.push({type:l,key:r,value:Rt(()=>{var u;return(u=e==null?void 0:e.proxy)==null?void 0:u[r]}),editable:typeof i.set=="function"})}return o}function i_(e){return Object.keys(e.attrs).map(t=>({type:"attrs",key:t,value:Rt(()=>e.attrs[t])}))}function l_(e){return Reflect.ownKeys(e.provides).map(t=>({type:"provided",key:t.toString(),value:Rt(()=>e.provides[t])}))}function u_(e,t){if(!(t!=null&&t.inject))return[];let n=[],o;return Array.isArray(t.inject)?n=t.inject.map(s=>({key:s,originalKey:s})):n=Reflect.ownKeys(t.inject).map(s=>{const r=t.inject[s];let i;return typeof r=="string"||typeof r=="symbol"?i=r:(i=r.from,o=r.default),{key:s,originalKey:i}}),n.map(({key:s,originalKey:r})=>({type:"injected",key:r&&s!==r?`${r.toString()} ➞ ${s.toString()}`:s.toString(),value:Rt(()=>e.ctx.hasOwnProperty(s)?e.ctx[s]:e.provides.hasOwnProperty(r)?e.provides[r]:o)}))}function a_(e){return Object.keys(e.refs).map(t=>({type:"template refs",key:t,value:Rt(()=>e.refs[t])}))}function c_(e){var t,n;const o=e.type.emits,s=Array.isArray(o)?o:Object.keys(o??{}),r=Object.keys((n=(t=e==null?void 0:e.vnode)==null?void 0:t.props)!=null?n:{}),i=[];for(const l of r){const[u,...a]=l.split(/(?=[A-Z])/);if(u==="on"){const c=a.join("-").toLowerCase(),f=s.includes(c);i.push({type:"event listeners",key:c,value:{_custom:{displayText:f?"✅ Declared":"⚠️ Not declared",key:f?"✅ Declared":"⚠️ Not declared",value:f?"✅ Declared":"⚠️ Not declared",tooltipText:f?null:`The event <code>${c}</code> is not declared in the <code>emits</code> option. It will leak into the component's attributes (<code>$attrs</code>).`}}})}}return i}function f_(e){const t=e_(e);return t_(e).concat(n_(e),s_(e),r_(e,t),i_(e),l_(e),u_(e,t),a_(e),c_(e))}function d_(e){var t;const n=hn(se.value,e.instanceId),o=fr(n),s=rt(n),r=(t=n==null?void 0:n.type)==null?void 0:t.__file,i=f_(n);return{id:o,name:s,file:r,state:i,instance:n}}w(),w();var p_=class{constructor(e){this.filter=e||""}isQualified(e){const t=rt(e);return Vl(t).toLowerCase().includes(this.filter)||Pp(t).toLowerCase().includes(this.filter)}};function h_(e){return new p_(e)}var __=class{constructor(e){this.captureIds=new Map;const{filterText:t="",maxDepth:n,recursively:o,api:s}=e;this.componentFilter=h_(t),this.maxDepth=n,this.recursively=o,this.api=s}getComponentTree(e){return this.captureIds=new Map,this.findQualifiedChildren(e,0)}getComponentParents(e){this.captureIds=new Map;const t=[];this.captureId(e);let n=e;for(;n=n.parent;)this.captureId(n),t.push(n);return t}captureId(e){if(!e)return null;const t=e.__VUE_DEVTOOLS_NEXT_UID__!=null?e.__VUE_DEVTOOLS_NEXT_UID__:fr(e);return e.__VUE_DEVTOOLS_NEXT_UID__=t,this.captureIds.has(t)?null:(this.captureIds.set(t,void 0),this.mark(e),t)}async capture(e,t){var n;if(!e)return null;const o=this.captureId(e),s=rt(e),r=this.getInternalInstanceChildren(e.subTree).filter(f=>!cr(f)),i=this.getComponentParents(e)||[],l=!!e.isDeactivated||i.some(f=>f.isDeactivated),u={uid:e.uid,id:o,name:s,renderKey:Zp(e.vnode?e.vnode.key:null),inactive:l,children:[],isFragment:ar(e),tags:typeof e.type!="function"?[]:[{label:"functional",textColor:5592405,backgroundColor:15658734}],autoOpen:this.recursively,file:e.type.__file||""};if((t<this.maxDepth||e.type.__isKeepAlive||i.some(f=>f.type.__isKeepAlive))&&(u.children=await Promise.all(r.map(f=>this.capture(f,t+1)).filter(Boolean))),this.isKeepAlive(e)){const f=this.getKeepAliveCachedInstances(e),h=r.map(d=>d.__VUE_DEVTOOLS_NEXT_UID__);for(const d of f)if(!h.includes(d.__VUE_DEVTOOLS_NEXT_UID__)){const _=await this.capture({...d,isDeactivated:!0},t+1);_&&u.children.push(_)}}const c=_n(e)[0];if(c!=null&&c.parentElement){const f=e.parent,h=f?_n(f):[];let d=c;const _=[];do _.push(Array.from(d.parentElement.childNodes).indexOf(d)),d=d.parentElement;while(d.parentElement&&h.length&&!h.includes(d));u.domOrder=_.reverse()}else u.domOrder=[-1];return(n=e.suspense)!=null&&n.suspenseKey&&(u.tags.push({label:e.suspense.suspenseKey,backgroundColor:14979812,textColor:16777215}),this.mark(e,!0)),this.api.visitComponentTree({treeNode:u,componentInstance:e,app:e.appContext.app,filter:this.componentFilter.filter}),u}async findQualifiedChildren(e,t){var n;if(this.componentFilter.isQualified(e)&&!((n=e.type.devtools)!=null&&n.hide))return[await this.capture(e,t)];if(e.subTree){const o=this.isKeepAlive(e)?this.getKeepAliveCachedInstances(e):this.getInternalInstanceChildren(e.subTree);return this.findQualifiedChildrenFromList(o,t)}else return[]}async findQualifiedChildrenFromList(e,t){return e=e.filter(n=>{var o;return!cr(n)&&!((o=n.type.devtools)!=null&&o.hide)}),this.componentFilter.filter?Array.prototype.concat.apply([],await Promise.all(e.map(n=>this.findQualifiedChildren(n,t)))):Promise.all(e.map(n=>this.capture(n,t)))}getInternalInstanceChildren(e,t=null){const n=[];if(e)if(e.component)t?n.push({...e.component,suspense:t}):n.push(e.component);else if(e.suspense){const o=e.suspense.isInFallback?"suspense fallback":"suspense default";n.push(...this.getInternalInstanceChildren(e.suspense.activeBranch,{...e.suspense,suspenseKey:o}))}else Array.isArray(e.children)&&e.children.forEach(o=>{o.component?t?n.push({...o.component,suspense:t}):n.push(o.component):n.push(...this.getInternalInstanceChildren(o,t))});return n.filter(o=>{var s;return!cr(o)&&!((s=o.type.devtools)!=null&&s.hide)})}mark(e,t=!1){const n=qe(e).instanceMap;(t||!n.has(e.__VUE_DEVTOOLS_NEXT_UID__))&&(n.set(e.__VUE_DEVTOOLS_NEXT_UID__,e),se.value.instanceMap=n)}isKeepAlive(e){return e.type.__isKeepAlive&&e.__v_cache}getKeepAliveCachedInstances(e){return Array.from(e.__v_cache.values()).map(t=>t.component).filter(Boolean)}};w(),w();var Go=new Map,Tr="performance";async function m_(e,t,n,o,s,r){const i=await qe(t);if(!i)return;const l=rt(o)||"Unknown Component",u=q.perfUniqueGroupId++,a=`${n}-${s}`;if(i.perfGroupIds.set(a,{groupId:u,time:r}),await e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"start"},title:l,subtitle:s,groupId:u}}),Go.has(a)){const{app:c,uid:f,instance:h,type:d,time:_}=Go.get(a);Go.delete(a),await Ou(e,c,f,h,d,_)}}function Ou(e,t,n,o,s,r){const i=qe(t);if(!i)return;const l=rt(o)||"Unknown Component",u=`${n}-${s}`,a=i.perfGroupIds.get(u);if(a){const c=a.groupId,f=a.time,h=r-f;e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"end",duration:{_custom:{type:"Duration",value:h,display:`${h} ms`}}},title:l,subtitle:s,groupId:c}})}else Go.set(u,{app:t,uid:n,instance:o,type:s,time:r})}var Au="component-event";function g_(e){jn&&(e.addTimelineLayer({id:"mouse",label:"Mouse",color:10768815}),["mousedown","mouseup","click","dblclick"].forEach(t=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.mouseEventEnabled||window.addEventListener(t,async n=>{await e.addTimelineEvent({layerId:"mouse",event:{time:Date.now(),data:{type:t,x:n.clientX,y:n.clientY},title:t}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:"keyboard",label:"Keyboard",color:8475055}),["keyup","keydown","keypress"].forEach(t=>{window.addEventListener(t,async n=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.keyboardEventEnabled||await e.addTimelineEvent({layerId:"keyboard",event:{time:Date.now(),data:{type:t,key:n.key,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,altKey:n.altKey,metaKey:n.metaKey},title:n.key}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:Au,label:"Component events",color:5226637}),ze.on.componentEmit(async(t,n,o,s)=>{if(!q.timelineLayersState.recordingState||!q.timelineLayersState.componentEventEnabled)return;const r=await qe(t);if(!r)return;const i=`${r.id}:${n.uid}`,l=rt(n)||"Unknown Component";e.addTimelineEvent({layerId:Au,event:{time:Date.now(),data:{component:{_custom:{type:"component-definition",display:l}},event:o,params:s},title:o,subtitle:`by ${l}`,meta:{componentId:i}}})}),e.addTimelineLayer({id:"performance",label:Tr,color:4307050}),ze.on.perfStart((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||m_(e,t,n,o,s,r)}),ze.on.perfEnd((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||Ou(e,t,n,o,s,r)}))}w();var v_=10,Xt=[];function y_(e){if(typeof window>"u")return;const t=window;if(e&&(t.$vm=e,Xt[0]!==e)){Xt.length>=v_&&Xt.pop();for(let n=Xt.length;n>0;n--)t[`$vm${n}`]=Xt[n]=Xt[n-1];t.$vm0=Xt[0]=e}}var Zt="components";function E_(e){return[{id:Zt,label:"Components",app:e},o=>{o.addInspector({id:Zt,label:"Components",treeFilterPlaceholder:"Search components"}),g_(o),o.on.getInspectorTree(async i=>{if(i.app===e&&i.inspectorId===Zt){const l=hn(se.value,i.instanceId);if(l){const u=new __({filterText:i.filter,maxDepth:100,recursively:!1,api:o});i.rootNodes=await u.getComponentTree(l)}}}),o.on.getInspectorState(async i=>{var l;if(i.app===e&&i.inspectorId===Zt){const u=d_({instanceId:i.nodeId}),a=u.instance,c=(l=u.instance)==null?void 0:l.appContext.app,f={componentInstance:a,app:c,instanceData:u};Xe.hooks.callHookWith(h=>{h.forEach(d=>d(f))},"inspectComponent"),i.state=u,y_(a)}}),o.on.editInspectorState(async i=>{i.app===e&&i.inspectorId===Zt&&(Eh(i),await o.sendInspectorState("components"))});const s=It(()=>{o.sendInspectorTree(Zt)},120),r=It(()=>{o.sendInspectorState(Zt)},120);ze.on.componentAdded(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await ur({app:i,uid:l,instance:a}),_=await qe(i);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,a),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&s()}),ze.on.componentUpdated(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await ur({app:i,uid:l,instance:a}),_=await qe(i);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,a),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&(s(),r())}),ze.on.componentRemoved(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await qe(i);if(!d)return;const _=await ur({app:i,uid:l,instance:a});d==null||d.instanceMap.delete(_),se.value.id===(d==null?void 0:d.id)&&(se.value.instanceMap=d.instanceMap),s()})}]}var Cu,Du;(Du=(Cu=O).__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__)!=null||(Cu.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__=new Set);function b_(e,t){return ze.setupDevToolsPlugin(e,t)}function Pu(e,t){const[n,o]=e;if(n.app!==t)return;const s=new Bh({plugin:{setupFn:o,descriptor:n},ctx:Xe});n.packageName==="vuex"&&s.on.editInspectorState(r=>{s.sendInspectorState(r.inspectorId)}),o(s)}function w_(e){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.delete(e)}function Or(e,t){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.has(e)||q.highPerfModeEnabled&&!(t!=null&&t.inspectingComponent)||(O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.add(e),Wn.forEach(n=>{Pu(n,e)}))}w(),w();var Gn="__VUE_DEVTOOLS_ROUTER__",gn="__VUE_DEVTOOLS_ROUTER_INFO__",Iu,Ru;(Ru=(Iu=O)[gn])!=null||(Iu[gn]={currentRoute:null,routes:[]});var ku,Nu;(Nu=(ku=O)[Gn])!=null||(ku[Gn]={}),new Proxy(O[gn],{get(e,t){return O[gn][t]}}),new Proxy(O[Gn],{get(e,t){if(t==="value")return O[Gn]}});function S_(e){const t=new Map;return((e==null?void 0:e.getRoutes())||[]).filter(n=>!t.has(n.path)&&t.set(n.path,1))}function Ar(e){return e.map(t=>{let{path:n,name:o,children:s,meta:r}=t;return s!=null&&s.length&&(s=Ar(s)),{path:n,name:o,children:s,meta:r}})}function x_(e){if(e){const{fullPath:t,hash:n,href:o,path:s,name:r,matched:i,params:l,query:u}=e;return{fullPath:t,hash:n,href:o,path:s,name:r,params:l,query:u,matched:Ar(i)}}return e}function Vu(e,t){function n(){var o;const s=(o=e.app)==null?void 0:o.config.globalProperties.$router,r=x_(s==null?void 0:s.currentRoute.value),i=Ar(S_(s)),l=console.warn;console.warn=()=>{},O[gn]={currentRoute:r?Ll(r):{},routes:Ll(i)},O[Gn]=s,console.warn=l}n(),ze.on.componentUpdated(It(()=>{var o;((o=t.value)==null?void 0:o.app)===e.app&&(n(),!q.highPerfModeEnabled&&Xe.hooks.callHook("routerInfoUpdated",{state:O[gn]}))},200))}function T_(e){return{async getInspectorTree(t){const n={...t,app:se.value.app,rootNodes:[]};return await new Promise(o=>{e.callHookWith(async s=>{await Promise.all(s.map(r=>r(n))),o()},"getInspectorTree")}),n.rootNodes},async getInspectorState(t){const n={...t,app:se.value.app,state:null},o={currentTab:`custom-inspector:${t.inspectorId}`};return await new Promise(s=>{e.callHookWith(async r=>{await Promise.all(r.map(i=>i(n,o))),s()},"getInspectorState")}),n.state},editInspectorState(t){const n=new Jl,o={...t,app:se.value.app,set:(s,r=t.path,i=t.state.value,l)=>{n.set(s,r,i,l||n.createDefaultSetCallback(t.state))}};e.callHookWith(s=>{s.forEach(r=>r(o))},"editInspectorState")},sendInspectorState(t){const n=Ko(t);e.callHook("sendInspectorState",{inspectorId:t,plugin:{descriptor:n.descriptor,setupFn:()=>({})}})},inspectComponentInspector(){return fh()},cancelInspectComponentInspector(){return ch()},getComponentRenderCode(t){const n=hn(se.value,t);if(n)return typeof(n==null?void 0:n.type)!="function"?n.render.toString():n.type.toString()},scrollToComponent(t){return dh({id:t})},openInEditor:kh,getVueInspector:_h,toggleApp(t,n){const o=kt.value.find(s=>s.id===t);o&&(gu(t),br(o),Vu(o,se),ou(),Or(o.app,n))},inspectDOM(t){const n=hn(se.value,t);if(n){const[o]=_n(n);o&&(O.__VUE_DEVTOOLS_INSPECT_DOM_TARGET__=o)}},updatePluginSettings(t,n,o){Lh(t,n,o)},getPluginSettings(t){return{options:Vh(t),values:Eu(t)}}}}w();var Lu,Mu;(Mu=(Lu=O).__VUE_DEVTOOLS_ENV__)!=null||(Lu.__VUE_DEVTOOLS_ENV__={vitePluginDetected:!1});function O_(){return O.__VUE_DEVTOOLS_ENV__}var $u=Oh(),Uu,Fu;(Fu=(Uu=O).__VUE_DEVTOOLS_KIT_CONTEXT__)!=null||(Uu.__VUE_DEVTOOLS_KIT_CONTEXT__={hooks:$u,get state(){return{...q,activeAppRecordId:se.id,activeAppRecord:se.value,appRecords:kt.value}},api:T_($u)});var Xe=O.__VUE_DEVTOOLS_KIT_CONTEXT__;w();var A_=Kp(Gp()),Bu,Hu,Nt=(Hu=(Bu=O).__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__)!=null?Hu:Bu.__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__={id:0,appIds:new Set};function C_(e,t){var n;return((n=e==null?void 0:e._component)==null?void 0:n.name)||`App ${t}`}function D_(e){var t,n,o,s;if(e._instance)return e._instance;if((n=(t=e._container)==null?void 0:t._vnode)!=null&&n.component)return(s=(o=e._container)==null?void 0:o._vnode)==null?void 0:s.component}function P_(e){const t=e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;t!=null&&(Nt.appIds.delete(t),Nt.id--)}function I_(e,t){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__!=null)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;let n=t??(Nt.id++).toString();if(t&&Nt.appIds.has(n)){let o=1;for(;Nt.appIds.has(`${t}_${o}`);)o++;n=`${t}_${o}`}return Nt.appIds.add(n),e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__=n,n}function R_(e,t){var n,o;const s=D_(e);if(s){Nt.id++;const r=C_(e,Nt.id.toString()),i=I_(e,(0,A_.default)(r)),[l]=_n(s),u={id:i,name:r,types:t,instanceMap:new Map,perfGroupIds:new Map,rootInstance:s,iframe:jn&&document!==(l==null?void 0:l.ownerDocument)?(o=(n=l==null?void 0:l.ownerDocument)==null?void 0:n.location)==null?void 0:o.pathname:void 0};e.__VUE_DEVTOOLS_NEXT_APP_RECORD__=u;const a=`${u.id}:root`;return u.instanceMap.set(a,u.rootInstance),u.rootInstance.__VUE_DEVTOOLS_NEXT_UID__=a,u}else return{}}w();function zu(e,t=!1){if(t){let i=function(u){try{const a=window.parent.__VUE_DEVTOOLS_GLOBAL_HOOK__;a&&u(a)}catch{}};const l={id:"vue-devtools-next",devtoolsVersion:"7.0",on:(u,a)=>{i(c=>{c.on(u,a)})},once:(u,a)=>{i(c=>{c.once(u,a)})},off:(u,a)=>{i(c=>{c.off(u,a)})},emit:(u,...a)=>{i(c=>{c.emit(u,...a)})}};Object.defineProperty(e,"__VUE_DEVTOOLS_GLOBAL_HOOK__",{get(){return l},configurable:!0})}function n(i){if(!i.__vdevtools__injected)try{i.__vdevtools__injected=!0;const l=()=>{try{i.contentWindow.__VUE_DEVTOOLS_IFRAME__=i;const u=i.contentDocument.createElement("script");u.textContent=`;(${zu.toString()})(window, true)`,i.contentDocument.documentElement.appendChild(u),u.parentNode.removeChild(u)}catch{}};l(),i.addEventListener("load",()=>l())}catch{}}function o(){if(typeof window>"u")return;const i=Array.from(document.querySelectorAll("iframe:not([data-vue-devtools-ignore])"));for(const l of i)n(l)}o();let s=0;const r=setInterval(()=>{o(),s++,s>=5&&clearInterval(r)},1e3)}function k_(){var e;zu(O),wr({vitePluginDetected:O_().vitePluginDetected});const t=((e=O.__VUE_DEVTOOLS_GLOBAL_HOOK__)==null?void 0:e.id)==="vue-devtools-next";if(O.__VUE_DEVTOOLS_GLOBAL_HOOK__&&t)return;const n=$h();if(O.__VUE_DEVTOOLS_HOOK_REPLAY__)try{O.__VUE_DEVTOOLS_HOOK_REPLAY__.forEach(o=>o(n)),O.__VUE_DEVTOOLS_HOOK_REPLAY__=[]}catch(o){console.error("[vue-devtools] Error during hook replay",o)}n.once("init",o=>{O.__VUE_DEVTOOLS_VUE2_APP_DETECTED__=!0,console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;"),console.log("%cVue DevTools v7 detected in your Vue2 project. v7 only supports Vue3 and will not work.","font-bold: 500; font-size: 14px;");const s="https://chromewebstore.google.com/detail/vuejs-devtools/iaajmlceplecbljialhhkmedjlpdblhp",r="https://addons.mozilla.org/firefox/addon/vue-js-devtools-v6-legacy";console.log(`%cThe legacy version of chrome extension that supports both Vue 2 and Vue 3 has been moved to %c ${s}`,"font-size: 14px;","text-decoration: underline; cursor: pointer;font-size: 14px;"),console.log(`%cThe legacy version of firefox extension that supports both Vue 2 and Vue 3 has been moved to %c ${r}`,"font-size: 14px;","text-decoration: underline; cursor: pointer;font-size: 14px;"),console.log("%cPlease install and enable only the legacy version for your Vue2 app.","font-bold: 500; font-size: 14px;"),console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;")}),ze.on.setupDevtoolsPlugin((o,s)=>{var r;Nh(o,s);const{app:i}=(r=se)!=null?r:{};o.settings&&bu(o.id,o.settings),i&&Pu([o,s],i)}),Yp(()=>{Wn.filter(([s])=>s.id!=="components").forEach(([s,r])=>{n.emit("devtools-plugin:setup",s,r,{target:"legacy"})})}),ze.on.vueAppInit(async(o,s,r)=>{const l={...R_(o,r),app:o,version:s};Ph(l),kt.value.length===1&&(br(l),gu(l.id),Vu(l,se),Or(l.app)),b_(...E_(l.app)),wr({connected:!0}),n.apps.push(o)}),ze.on.vueAppUnmount(async o=>{const s=kt.value.filter(r=>r.app!==o);s.length===0&&wr({connected:!1}),Ih(o),P_(o),se.value.app===o&&(br(s[0]),Xe.hooks.callHook("sendActiveAppUpdatedToClient")),O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.splice(O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.indexOf(o),1),w_(o)}),Uh(n),O.__VUE_DEVTOOLS_GLOBAL_HOOK__?xp||Object.assign(__VUE_DEVTOOLS_GLOBAL_HOOK__,n):Object.defineProperty(O,"__VUE_DEVTOOLS_GLOBAL_HOOK__",{get(){return n},configurable:!0})}w();function N_(e){q.highPerfModeEnabled=e??!q.highPerfModeEnabled,!e&&se.value&&Or(se.value.app)}w(),w(),w();function V_(e){q.devtoolsClientDetected={...q.devtoolsClientDetected,...e};const t=Object.values(q.devtoolsClientDetected).some(Boolean);N_(!t)}var ju,Ku;(Ku=(ju=O).__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__)!=null||(ju.__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__=V_),w(),w(),w(),w(),w(),w(),w();var L_=class{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}},Wu=class{constructor(e){this.generateIdentifier=e,this.kv=new L_}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}},M_=class extends Wu{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){typeof t=="object"?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}};w(),w();function $_(e){if("values"in Object)return Object.values(e);const t=[];for(const n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t}function U_(e,t){const n=$_(e);if("find"in n)return n.find(t);const o=n;for(let s=0;s<o.length;s++){const r=o[s];if(t(r))return r}}function vn(e,t){Object.entries(e).forEach(([n,o])=>t(o,n))}function Yo(e,t){return e.indexOf(t)!==-1}function Gu(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(t(o))return o}}var F_=class{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return U_(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}};w(),w();var B_=e=>Object.prototype.toString.call(e).slice(8,-1),Yu=e=>typeof e>"u",H_=e=>e===null,Yn=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,Cr=e=>Yn(e)&&Object.keys(e).length===0,Vt=e=>Array.isArray(e),z_=e=>typeof e=="string",j_=e=>typeof e=="number"&&!isNaN(e),K_=e=>typeof e=="boolean",W_=e=>e instanceof RegExp,qn=e=>e instanceof Map,Xn=e=>e instanceof Set,qu=e=>B_(e)==="Symbol",G_=e=>e instanceof Date&&!isNaN(e.valueOf()),Y_=e=>e instanceof Error,Xu=e=>typeof e=="number"&&isNaN(e),q_=e=>K_(e)||H_(e)||Yu(e)||j_(e)||z_(e)||qu(e),X_=e=>typeof e=="bigint",Z_=e=>e===1/0||e===-1/0,J_=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),Q_=e=>e instanceof URL;w();var Zu=e=>e.replace(/\./g,"\\."),Dr=e=>e.map(String).map(Zu).join("."),Zn=e=>{const t=[];let n="";for(let s=0;s<e.length;s++){let r=e.charAt(s);if(r==="\\"&&e.charAt(s+1)==="."){n+=".",s++;continue}if(r==="."){t.push(n),n="";continue}n+=r}const o=n;return t.push(o),t};w();function lt(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Ju=[lt(Yu,"undefined",()=>null,()=>{}),lt(X_,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),lt(G_,"Date",e=>e.toISOString(),e=>new Date(e)),lt(Y_,"Error",(e,t)=>{const n={name:e.name,message:e.message};return t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n},(e,t)=>{const n=new Error(e.message);return n.name=e.name,n.stack=e.stack,t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n}),lt(W_,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),n=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,n)}),lt(Xn,"set",e=>[...e.values()],e=>new Set(e)),lt(qn,"map",e=>[...e.entries()],e=>new Map(e)),lt(e=>Xu(e)||Z_(e),"number",e=>Xu(e)?"NaN":e>0?"Infinity":"-Infinity",Number),lt(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),lt(Q_,"URL",e=>e.toString(),e=>new URL(e))];function qo(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Qu=qo((e,t)=>qu(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,n)=>{const o=n.symbolRegistry.getValue(t[1]);if(!o)throw new Error("Trying to deserialize unknown symbol");return o}),em=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),ea=qo(J_,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const n=em[t[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(e)});function ta(e,t){return e!=null&&e.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}var na=qo(ta,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const n=t.classRegistry.getAllowedProps(e.constructor);if(!n)return{...e};const o={};return n.forEach(s=>{o[s]=e[s]}),o},(e,t,n)=>{const o=n.classRegistry.getValue(t[1]);if(!o)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(o.prototype),e)}),oa=qo((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,n)=>{const o=n.customTransformerRegistry.findByName(t[1]);if(!o)throw new Error("Trying to deserialize unknown custom value");return o.deserialize(e)}),tm=[na,Qu,oa,ea],sa=(e,t)=>{const n=Gu(tm,s=>s.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation(e,t)};const o=Gu(Ju,s=>s.isApplicable(e,t));if(o)return{value:o.transform(e,t),type:o.annotation}},ra={};Ju.forEach(e=>{ra[e.annotation]=e});var nm=(e,t,n)=>{if(Vt(t))switch(t[0]){case"symbol":return Qu.untransform(e,t,n);case"class":return na.untransform(e,t,n);case"custom":return oa.untransform(e,t,n);case"typed-array":return ea.untransform(e,t,n);default:throw new Error("Unknown transformation: "+t)}else{const o=ra[t];if(!o)throw new Error("Unknown transformation: "+t);return o.untransform(e,n)}};w();var yn=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const n=e.keys();for(;t>0;)n.next(),t--;return n.next().value};function ia(e){if(Yo(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(Yo(e,"prototype"))throw new Error("prototype is not allowed as a property");if(Yo(e,"constructor"))throw new Error("constructor is not allowed as a property")}var om=(e,t)=>{ia(t);for(let n=0;n<t.length;n++){const o=t[n];if(Xn(e))e=yn(e,+o);else if(qn(e)){const s=+o,r=+t[++n]==0?"key":"value",i=yn(e,s);switch(r){case"key":e=i;break;case"value":e=e.get(i);break}}else e=e[o]}return e},Pr=(e,t,n)=>{if(ia(t),t.length===0)return n(e);let o=e;for(let r=0;r<t.length-1;r++){const i=t[r];if(Vt(o)){const l=+i;o=o[l]}else if(Yn(o))o=o[i];else if(Xn(o)){const l=+i;o=yn(o,l)}else if(qn(o)){if(r===t.length-2)break;const u=+i,a=+t[++r]==0?"key":"value",c=yn(o,u);switch(a){case"key":o=c;break;case"value":o=o.get(c);break}}}const s=t[t.length-1];if(Vt(o)?o[+s]=n(o[+s]):Yn(o)&&(o[s]=n(o[s])),Xn(o)){const r=yn(o,+s),i=n(r);r!==i&&(o.delete(r),o.add(i))}if(qn(o)){const r=+t[t.length-2],i=yn(o,r);switch(+s==0?"key":"value"){case"key":{const u=n(i);o.set(u,o.get(i)),u!==i&&o.delete(i);break}case"value":{o.set(i,n(o.get(i)));break}}}return e};function Ir(e,t,n=[]){if(!e)return;if(!Vt(e)){vn(e,(r,i)=>Ir(r,t,[...n,...Zn(i)]));return}const[o,s]=e;s&&vn(s,(r,i)=>{Ir(r,t,[...n,...Zn(i)])}),t(o,n)}function sm(e,t,n){return Ir(t,(o,s)=>{e=Pr(e,s,r=>nm(r,o,n))}),e}function rm(e,t){function n(o,s){const r=om(e,Zn(s));o.map(Zn).forEach(i=>{e=Pr(e,i,()=>r)})}if(Vt(t)){const[o,s]=t;o.forEach(r=>{e=Pr(e,Zn(r),()=>e)}),s&&vn(s,n)}else vn(t,n);return e}var im=(e,t)=>Yn(e)||Vt(e)||qn(e)||Xn(e)||ta(e,t);function lm(e,t,n){const o=n.get(e);o?o.push(t):n.set(e,[t])}function um(e,t){const n={};let o;return e.forEach(s=>{if(s.length<=1)return;t||(s=s.map(l=>l.map(String)).sort((l,u)=>l.length-u.length));const[r,...i]=s;r.length===0?o=i.map(Dr):n[Dr(r)]=i.map(Dr)}),o?Cr(n)?[o]:[o,n]:Cr(n)?void 0:n}var la=(e,t,n,o,s=[],r=[],i=new Map)=>{var l;const u=q_(e);if(!u){lm(e,s,t);const _=i.get(e);if(_)return o?{transformedValue:null}:_}if(!im(e,n)){const _=sa(e,n),v=_?{transformedValue:_.value,annotations:[_.type]}:{transformedValue:e};return u||i.set(e,v),v}if(Yo(r,e))return{transformedValue:null};const a=sa(e,n),c=(l=a==null?void 0:a.value)!=null?l:e,f=Vt(c)?[]:{},h={};vn(c,(_,v)=>{if(v==="__proto__"||v==="constructor"||v==="prototype")throw new Error(`Detected property ${v}. This is a prototype pollution risk, please remove it from your object.`);const y=la(_,t,n,o,[...s,v],[...r,e],i);f[v]=y.transformedValue,Vt(y.annotations)?h[v]=y.annotations:Yn(y.annotations)&&vn(y.annotations,(g,x)=>{h[Zu(v)+"."+x]=g})});const d=Cr(h)?{transformedValue:f,annotations:a?[a.type]:void 0}:{transformedValue:f,annotations:a?[a.type,h]:h};return u||i.set(e,d),d};w(),w();function ua(e){return Object.prototype.toString.call(e).slice(8,-1)}function aa(e){return ua(e)==="Array"}function am(e){if(ua(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function cm(e,t,n,o,s){const r={}.propertyIsEnumerable.call(o,t)?"enumerable":"nonenumerable";r==="enumerable"&&(e[t]=n),s&&r==="nonenumerable"&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}function Rr(e,t={}){if(aa(e))return e.map(s=>Rr(s,t));if(!am(e))return e;const n=Object.getOwnPropertyNames(e),o=Object.getOwnPropertySymbols(e);return[...n,...o].reduce((s,r)=>{if(aa(t.props)&&!t.props.includes(r))return s;const i=e[r],l=Rr(i,t);return cm(s,r,l,e,t.nonenumerable),s},{})}var ce=class{constructor({dedupe:e=!1}={}){this.classRegistry=new M_,this.symbolRegistry=new Wu(t=>{var n;return(n=t.description)!=null?n:""}),this.customTransformerRegistry=new F_,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const t=new Map,n=la(e,t,this,this.dedupe),o={json:n.transformedValue};n.annotations&&(o.meta={...o.meta,values:n.annotations});const s=um(t,this.dedupe);return s&&(o.meta={...o.meta,referentialEqualities:s}),o}deserialize(e){const{json:t,meta:n}=e;let o=Rr(t);return n!=null&&n.values&&(o=sm(o,n.values,this)),n!=null&&n.referentialEqualities&&(o=rm(o,n.referentialEqualities)),o}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}};ce.defaultInstance=new ce,ce.serialize=ce.defaultInstance.serialize.bind(ce.defaultInstance),ce.deserialize=ce.defaultInstance.deserialize.bind(ce.defaultInstance),ce.stringify=ce.defaultInstance.stringify.bind(ce.defaultInstance),ce.parse=ce.defaultInstance.parse.bind(ce.defaultInstance),ce.registerClass=ce.defaultInstance.registerClass.bind(ce.defaultInstance),ce.registerSymbol=ce.defaultInstance.registerSymbol.bind(ce.defaultInstance),ce.registerCustom=ce.defaultInstance.registerCustom.bind(ce.defaultInstance),ce.allowErrorProps=ce.defaultInstance.allowErrorProps.bind(ce.defaultInstance),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var fm="iframe:server-context";function dm(e){O[fm]=e}w(),w(),w(),w(),w(),w(),w(),w(),w();var ca,fa;(fa=(ca=O).__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__)!=null||(ca.__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__=[]);var da,pa;(pa=(da=O).__VUE_DEVTOOLS_KIT_RPC_CLIENT__)!=null||(da.__VUE_DEVTOOLS_KIT_RPC_CLIENT__=null);var ha,_a;(_a=(ha=O).__VUE_DEVTOOLS_KIT_RPC_SERVER__)!=null||(ha.__VUE_DEVTOOLS_KIT_RPC_SERVER__=null);var ma,ga;(ga=(ma=O).__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__)!=null||(ma.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__=null);var va,ya;(ya=(va=O).__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__)!=null||(va.__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__=null);var Ea,ba;(ba=(Ea=O).__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__)!=null||(Ea.__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__=null);function pm(){return O.__VUE_DEVTOOLS_KIT_RPC_CLIENT__}function wa(){return O.__VUE_DEVTOOLS_KIT_RPC_SERVER__}function hm(){return O.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__}w(),w(),w(),w(),w(),w(),w();var Sa={hook:ze,init:()=>{k_()},get ctx(){return Xe},get api(){return Xe.api}};function _m(){var e;return(e=O.__VUE_DEVTOOLS_CLIENT_URL__)!=null?e:(()=>{if(jn){const t=document.querySelector("meta[name=__VUE_DEVTOOLS_CLIENT_URL__]");if(t)return t.getAttribute("content")}return""})()}function kr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?kr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}var mm={run:e=>e()},gm=()=>mm,xa=typeof console.createTask<"u"?console.createTask:gm;function vm(e,t){const n=t.shift(),o=xa(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function ym(e,t){const n=t.shift(),o=xa(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function Nr(e,t){for(const n of[...e])n(t)}var Em=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,n={}){if(!e||typeof t!="function")return()=>{};const o=e;let s;for(;this._deprecatedHooks[e];)s=this._deprecatedHooks[e],e=s.to;if(s&&!n.allowDeprecated){let r=s.message;r||(r=`${o} hook has been deprecated`+(s.to?`, please use ${s.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(r)||(console.warn(r),this._deprecatedMessages.add(r))}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let n,o=(...s)=>(typeof n=="function"&&n(),n=void 0,o=void 0,t(...s));return n=this.hook(e,o),n}removeHook(e,t){if(this._hooks[e]){const n=this._hooks[e].indexOf(t);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]=typeof t=="string"?{to:t}:t;const n=this._hooks[e]||[];delete this._hooks[e];for(const o of n)this.hook(e,o)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=kr(e),n=Object.keys(t).map(o=>this.hook(o,t[o]));return()=>{for(const o of n.splice(0,n.length))o()}}removeHooks(e){const t=kr(e);for(const n in t)this.removeHook(n,t[n])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(vm,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(ym,e,...t)}callHookWith(e,t,...n){const o=this._before||this._after?{name:t,args:n,context:{}}:void 0;this._before&&Nr(this._before,o);const s=e(t in this._hooks?[...this._hooks[t]]:[],n);return s instanceof Promise?s.finally(()=>{this._after&&o&&Nr(this._after,o)}):(this._after&&o&&Nr(this._after,o),s)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){const t=this._before.indexOf(e);t!==-1&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){const t=this._after.indexOf(e);t!==-1&&this._after.splice(t,1)}}}};function Ta(){return new Em}Ta(),new Proxy({value:{},functions:{}},{get(e,t){const n=pm();if(t==="value")return n;if(t==="functions")return n.$functions}});var Oa=new Proxy({value:{},functions:{}},{get(e,t){const n=wa();if(t==="value")return n;if(t==="functions")return n.functions}});function bm(e){let t=null;const n=120;function o(){Oa.value.clients.length>0&&(e(),clearTimeout(t))}t=setInterval(()=>{o()},n)}Ta(),new Proxy({value:{},functions:{}},{get(e,t){const n=hm();if(t==="value")return n;if(t==="functions")return n==null?void 0:n.$functions}});const wm=["top","right","bottom","left"],Aa=["start","end"],Ca=wm.reduce((e,t)=>e.concat(t,t+"-"+Aa[0],t+"-"+Aa[1]),[]),Jn=Math.min,Jt=Math.max,Sm={left:"right",right:"left",bottom:"top",top:"bottom"},xm={start:"end",end:"start"};function Vr(e,t,n){return Jt(e,Jn(t,n))}function Qt(e,t){return typeof e=="function"?e(t):e}function ut(e){return e.split("-")[0]}function Ze(e){return e.split("-")[1]}function Da(e){return e==="x"?"y":"x"}function Lr(e){return e==="y"?"height":"width"}function en(e){return["top","bottom"].includes(ut(e))?"y":"x"}function Mr(e){return Da(en(e))}function Pa(e,t,n){n===void 0&&(n=!1);const o=Ze(e),s=Mr(e),r=Lr(s);let i=s==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(i=Zo(i)),[i,Zo(i)]}function Tm(e){const t=Zo(e);return[Xo(e),t,Xo(t)]}function Xo(e){return e.replace(/start|end/g,t=>xm[t])}function Om(e,t,n){const o=["left","right"],s=["right","left"],r=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:o:t?o:s;case"left":case"right":return t?r:i;default:return[]}}function Am(e,t,n,o){const s=Ze(e);let r=Om(ut(e),n==="start",o);return s&&(r=r.map(i=>i+"-"+s),t&&(r=r.concat(r.map(Xo)))),r}function Zo(e){return e.replace(/left|right|bottom|top/g,t=>Sm[t])}function Cm(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ia(e){return typeof e!="number"?Cm(e):{top:e,right:e,bottom:e,left:e}}function Qn(e){const{x:t,y:n,width:o,height:s}=e;return{width:o,height:s,top:n,left:t,right:t+o,bottom:n+s,x:t,y:n}}function Ra(e,t,n){let{reference:o,floating:s}=e;const r=en(t),i=Mr(t),l=Lr(i),u=ut(t),a=r==="y",c=o.x+o.width/2-s.width/2,f=o.y+o.height/2-s.height/2,h=o[l]/2-s[l]/2;let d;switch(u){case"top":d={x:c,y:o.y-s.height};break;case"bottom":d={x:c,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:f};break;case"left":d={x:o.x-s.width,y:f};break;default:d={x:o.x,y:o.y}}switch(Ze(t)){case"start":d[i]-=h*(n&&a?-1:1);break;case"end":d[i]+=h*(n&&a?-1:1);break}return d}const Dm=async(e,t,n)=>{const{placement:o="bottom",strategy:s="absolute",middleware:r=[],platform:i}=n,l=r.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:c,y:f}=Ra(a,o,u),h=o,d={},_=0;for(let v=0;v<l.length;v++){const{name:y,fn:g}=l[v],{x,y:D,data:b,reset:P}=await g({x:c,y:f,initialPlacement:o,placement:h,strategy:s,middlewareData:d,rects:a,platform:i,elements:{reference:e,floating:t}});c=x??c,f=D??f,d={...d,[y]:{...d[y],...b}},P&&_<=50&&(_++,typeof P=="object"&&(P.placement&&(h=P.placement),P.rects&&(a=P.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):P.rects),{x:c,y:f}=Ra(a,h,u)),v=-1)}return{x:c,y:f,placement:h,strategy:s,middlewareData:d}};async function Jo(e,t){var n;t===void 0&&(t={});const{x:o,y:s,platform:r,rects:i,elements:l,strategy:u}=e,{boundary:a="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:h=!1,padding:d=0}=Qt(t,e),_=Ia(d),y=l[h?f==="floating"?"reference":"floating":f],g=Qn(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(y)))==null||n?y:y.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:a,rootBoundary:c,strategy:u})),x=f==="floating"?{x:o,y:s,width:i.floating.width,height:i.floating.height}:i.reference,D=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),b=await(r.isElement==null?void 0:r.isElement(D))?await(r.getScale==null?void 0:r.getScale(D))||{x:1,y:1}:{x:1,y:1},P=Qn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:x,offsetParent:D,strategy:u}):x);return{top:(g.top-P.top+_.top)/b.y,bottom:(P.bottom-g.bottom+_.bottom)/b.y,left:(g.left-P.left+_.left)/b.x,right:(P.right-g.right+_.right)/b.x}}const Pm=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:s,rects:r,platform:i,elements:l,middlewareData:u}=t,{element:a,padding:c=0}=Qt(e,t)||{};if(a==null)return{};const f=Ia(c),h={x:n,y:o},d=Mr(s),_=Lr(d),v=await i.getDimensions(a),y=d==="y",g=y?"top":"left",x=y?"bottom":"right",D=y?"clientHeight":"clientWidth",b=r.reference[_]+r.reference[d]-h[d]-r.floating[_],P=h[d]-r.reference[d],U=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a));let H=U?U[D]:0;(!H||!await(i.isElement==null?void 0:i.isElement(U)))&&(H=l.floating[D]||r.floating[_]);const W=b/2-P/2,$=H/2-v[_]/2-1,I=Jn(f[g],$),R=Jn(f[x],$),F=I,z=H-v[_]-R,Q=H/2-v[_]/2+W,Ee=Vr(F,Q,z),Z=!u.arrow&&Ze(s)!=null&&Q!==Ee&&r.reference[_]/2-(Q<F?I:R)-v[_]/2<0,L=Z?Q<F?Q-F:Q-z:0;return{[d]:h[d]+L,data:{[d]:Ee,centerOffset:Q-Ee-L,...Z&&{alignmentOffset:L}},reset:Z}}});function Im(e,t,n){return(e?[...n.filter(s=>Ze(s)===e),...n.filter(s=>Ze(s)!==e)]:n.filter(s=>ut(s)===s)).filter(s=>e?Ze(s)===e||(t?Xo(s)!==s:!1):!0)}const Rm=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,s;const{rects:r,middlewareData:i,placement:l,platform:u,elements:a}=t,{crossAxis:c=!1,alignment:f,allowedPlacements:h=Ca,autoAlignment:d=!0,..._}=Qt(e,t),v=f!==void 0||h===Ca?Im(f||null,d,h):h,y=await Jo(t,_),g=((n=i.autoPlacement)==null?void 0:n.index)||0,x=v[g];if(x==null)return{};const D=Pa(x,r,await(u.isRTL==null?void 0:u.isRTL(a.floating)));if(l!==x)return{reset:{placement:v[0]}};const b=[y[ut(x)],y[D[0]],y[D[1]]],P=[...((o=i.autoPlacement)==null?void 0:o.overflows)||[],{placement:x,overflows:b}],U=v[g+1];if(U)return{data:{index:g+1,overflows:P},reset:{placement:U}};const H=P.map(I=>{const R=Ze(I.placement);return[I.placement,R&&c?I.overflows.slice(0,2).reduce((F,z)=>F+z,0):I.overflows[0],I.overflows]}).sort((I,R)=>I[1]-R[1]),$=((s=H.filter(I=>I[2].slice(0,Ze(I[0])?2:3).every(R=>R<=0))[0])==null?void 0:s[0])||H[0][0];return $!==l?{data:{index:g+1,overflows:P},reset:{placement:$}}:{}}}},km=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:s,middlewareData:r,rects:i,initialPlacement:l,platform:u,elements:a}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:v=!0,...y}=Qt(e,t);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const g=ut(s),x=en(l),D=ut(l)===l,b=await(u.isRTL==null?void 0:u.isRTL(a.floating)),P=h||(D||!v?[Zo(l)]:Tm(l)),U=_!=="none";!h&&U&&P.push(...Am(l,v,_,b));const H=[l,...P],W=await Jo(t,y),$=[];let I=((o=r.flip)==null?void 0:o.overflows)||[];if(c&&$.push(W[g]),f){const Q=Pa(s,i,b);$.push(W[Q[0]],W[Q[1]])}if(I=[...I,{placement:s,overflows:$}],!$.every(Q=>Q<=0)){var R,F;const Q=(((R=r.flip)==null?void 0:R.index)||0)+1,Ee=H[Q];if(Ee)return{data:{index:Q,overflows:I},reset:{placement:Ee}};let Z=(F=I.filter(L=>L.overflows[0]<=0).sort((L,X)=>L.overflows[1]-X.overflows[1])[0])==null?void 0:F.placement;if(!Z)switch(d){case"bestFit":{var z;const L=(z=I.filter(X=>{if(U){const ue=en(X.placement);return ue===x||ue==="y"}return!0}).map(X=>[X.placement,X.overflows.filter(ue=>ue>0).reduce((ue,Ue)=>ue+Ue,0)]).sort((X,ue)=>X[1]-ue[1])[0])==null?void 0:z[0];L&&(Z=L);break}case"initialPlacement":Z=l;break}if(s!==Z)return{reset:{placement:Z}}}return{}}}};async function Nm(e,t){const{placement:n,platform:o,elements:s}=e,r=await(o.isRTL==null?void 0:o.isRTL(s.floating)),i=ut(n),l=Ze(n),u=en(n)==="y",a=["left","top"].includes(i)?-1:1,c=r&&u?-1:1,f=Qt(t,e);let{mainAxis:h,crossAxis:d,alignmentAxis:_}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof _=="number"&&(d=l==="end"?_*-1:_),u?{x:d*c,y:h*a}:{x:h*a,y:d*c}}const Vm=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:s,y:r,placement:i,middlewareData:l}=t,u=await Nm(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:s+u.x,y:r+u.y,data:{...u,placement:i}}}}},Lm=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:s}=t,{mainAxis:r=!0,crossAxis:i=!1,limiter:l={fn:y=>{let{x:g,y:x}=y;return{x:g,y:x}}},...u}=Qt(e,t),a={x:n,y:o},c=await Jo(t,u),f=en(ut(s)),h=Da(f);let d=a[h],_=a[f];if(r){const y=h==="y"?"top":"left",g=h==="y"?"bottom":"right",x=d+c[y],D=d-c[g];d=Vr(x,d,D)}if(i){const y=f==="y"?"top":"left",g=f==="y"?"bottom":"right",x=_+c[y],D=_-c[g];_=Vr(x,_,D)}const v=l.fn({...t,[h]:d,[f]:_});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[h]:r,[f]:i}}}}}},Mm=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:s,rects:r,platform:i,elements:l}=t,{apply:u=()=>{},...a}=Qt(e,t),c=await Jo(t,a),f=ut(s),h=Ze(s),d=en(s)==="y",{width:_,height:v}=r.floating;let y,g;f==="top"||f==="bottom"?(y=f,g=h===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(g=f,y=h==="end"?"top":"bottom");const x=v-c.top-c.bottom,D=_-c.left-c.right,b=Jn(v-c[y],x),P=Jn(_-c[g],D),U=!t.middlewareData.shift;let H=b,W=P;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(W=D),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(H=x),U&&!h){const I=Jt(c.left,0),R=Jt(c.right,0),F=Jt(c.top,0),z=Jt(c.bottom,0);d?W=_-2*(I!==0||R!==0?I+R:Jt(c.left,c.right)):H=v-2*(F!==0||z!==0?F+z:Jt(c.top,c.bottom))}await u({...t,availableWidth:W,availableHeight:H});const $=await i.getDimensions(l.floating);return _!==$.width||v!==$.height?{reset:{rects:!0}}:{}}}};function je(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function at(e){return je(e).getComputedStyle(e)}const ka=Math.min,eo=Math.max,Qo=Math.round;function Na(e){const t=at(e);let n=parseFloat(t.width),o=parseFloat(t.height);const s=e.offsetWidth,r=e.offsetHeight,i=Qo(n)!==s||Qo(o)!==r;return i&&(n=s,o=r),{width:n,height:o,fallback:i}}function Lt(e){return La(e)?(e.nodeName||"").toLowerCase():""}let es;function Va(){if(es)return es;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(es=e.brands.map(t=>t.brand+"/"+t.version).join(" "),es):navigator.userAgent}function ct(e){return e instanceof je(e).HTMLElement}function Mt(e){return e instanceof je(e).Element}function La(e){return e instanceof je(e).Node}function Ma(e){return typeof ShadowRoot>"u"?!1:e instanceof je(e).ShadowRoot||e instanceof ShadowRoot}function ts(e){const{overflow:t,overflowX:n,overflowY:o,display:s}=at(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(s)}function $m(e){return["table","td","th"].includes(Lt(e))}function $r(e){const t=/firefox/i.test(Va()),n=at(e),o=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!o&&o!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(s=>n.willChange.includes(s))||["paint","layout","strict","content"].some(s=>{const r=n.contain;return r!=null&&r.includes(s)})}function $a(){return!/^((?!chrome|android).)*safari/i.test(Va())}function Ur(e){return["html","body","#document"].includes(Lt(e))}function Ua(e){return Mt(e)?e:e.contextElement}const Fa={x:1,y:1};function En(e){const t=Ua(e);if(!ct(t))return Fa;const n=t.getBoundingClientRect(),{width:o,height:s,fallback:r}=Na(t);let i=(r?Qo(n.width):n.width)/o,l=(r?Qo(n.height):n.height)/s;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}function to(e,t,n,o){var s,r;t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),l=Ua(e);let u=Fa;t&&(o?Mt(o)&&(u=En(o)):u=En(e));const a=l?je(l):window,c=!$a()&&n;let f=(i.left+(c&&((s=a.visualViewport)==null?void 0:s.offsetLeft)||0))/u.x,h=(i.top+(c&&((r=a.visualViewport)==null?void 0:r.offsetTop)||0))/u.y,d=i.width/u.x,_=i.height/u.y;if(l){const v=je(l),y=o&&Mt(o)?je(o):o;let g=v.frameElement;for(;g&&o&&y!==v;){const x=En(g),D=g.getBoundingClientRect(),b=getComputedStyle(g);D.x+=(g.clientLeft+parseFloat(b.paddingLeft))*x.x,D.y+=(g.clientTop+parseFloat(b.paddingTop))*x.y,f*=x.x,h*=x.y,d*=x.x,_*=x.y,f+=D.x,h+=D.y,g=je(g).frameElement}}return{width:d,height:_,top:h,right:f+d,bottom:h+_,left:f,x:f,y:h}}function $t(e){return((La(e)?e.ownerDocument:e.document)||window.document).documentElement}function ns(e){return Mt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Ba(e){return to($t(e)).left+ns(e).scrollLeft}function no(e){if(Lt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ma(e)&&e.host||$t(e);return Ma(t)?t.host:t}function Ha(e){const t=no(e);return Ur(t)?t.ownerDocument.body:ct(t)&&ts(t)?t:Ha(t)}function os(e,t){var n;t===void 0&&(t=[]);const o=Ha(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),r=je(o);return s?t.concat(r,r.visualViewport||[],ts(o)?o:[]):t.concat(o,os(o))}function za(e,t,n){return t==="viewport"?Qn(function(o,s){const r=je(o),i=$t(o),l=r.visualViewport;let u=i.clientWidth,a=i.clientHeight,c=0,f=0;if(l){u=l.width,a=l.height;const h=$a();(h||!h&&s==="fixed")&&(c=l.offsetLeft,f=l.offsetTop)}return{width:u,height:a,x:c,y:f}}(e,n)):Mt(t)?Qn(function(o,s){const r=to(o,!0,s==="fixed"),i=r.top+o.clientTop,l=r.left+o.clientLeft,u=ct(o)?En(o):{x:1,y:1};return{width:o.clientWidth*u.x,height:o.clientHeight*u.y,x:l*u.x,y:i*u.y}}(t,n)):Qn(function(o){const s=$t(o),r=ns(o),i=o.ownerDocument.body,l=eo(s.scrollWidth,s.clientWidth,i.scrollWidth,i.clientWidth),u=eo(s.scrollHeight,s.clientHeight,i.scrollHeight,i.clientHeight);let a=-r.scrollLeft+Ba(o);const c=-r.scrollTop;return at(i).direction==="rtl"&&(a+=eo(s.clientWidth,i.clientWidth)-l),{width:l,height:u,x:a,y:c}}($t(e)))}function ja(e){return ct(e)&&at(e).position!=="fixed"?e.offsetParent:null}function Ka(e){const t=je(e);let n=ja(e);for(;n&&$m(n)&&at(n).position==="static";)n=ja(n);return n&&(Lt(n)==="html"||Lt(n)==="body"&&at(n).position==="static"&&!$r(n))?t:n||function(o){let s=no(o);for(;ct(s)&&!Ur(s);){if($r(s))return s;s=no(s)}return null}(e)||t}function Um(e,t,n){const o=ct(t),s=$t(t),r=to(e,!0,n==="fixed",t);let i={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(o||!o&&n!=="fixed")if((Lt(t)!=="body"||ts(s))&&(i=ns(t)),ct(t)){const u=to(t,!0);l.x=u.x+t.clientLeft,l.y=u.y+t.clientTop}else s&&(l.x=Ba(s));return{x:r.left+i.scrollLeft-l.x,y:r.top+i.scrollTop-l.y,width:r.width,height:r.height}}const Fm={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:s}=e;const r=n==="clippingAncestors"?function(a,c){const f=c.get(a);if(f)return f;let h=os(a).filter(y=>Mt(y)&&Lt(y)!=="body"),d=null;const _=at(a).position==="fixed";let v=_?no(a):a;for(;Mt(v)&&!Ur(v);){const y=at(v),g=$r(v);(_?g||d:g||y.position!=="static"||!d||!["absolute","fixed"].includes(d.position))?d=y:h=h.filter(x=>x!==v),v=no(v)}return c.set(a,h),h}(t,this._c):[].concat(n),i=[...r,o],l=i[0],u=i.reduce((a,c)=>{const f=za(t,c,s);return a.top=eo(f.top,a.top),a.right=ka(f.right,a.right),a.bottom=ka(f.bottom,a.bottom),a.left=eo(f.left,a.left),a},za(t,l,s));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:o}=e;const s=ct(n),r=$t(n);if(n===r)return t;let i={scrollLeft:0,scrollTop:0},l={x:1,y:1};const u={x:0,y:0};if((s||!s&&o!=="fixed")&&((Lt(n)!=="body"||ts(r))&&(i=ns(n)),ct(n))){const a=to(n);l=En(n),u.x=a.x+n.clientLeft,u.y=a.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-i.scrollLeft*l.x+u.x,y:t.y*l.y-i.scrollTop*l.y+u.y}},isElement:Mt,getDimensions:function(e){return ct(e)?Na(e):e.getBoundingClientRect()},getOffsetParent:Ka,getDocumentElement:$t,getScale:En,async getElementRects(e){let{reference:t,floating:n,strategy:o}=e;const s=this.getOffsetParent||Ka,r=this.getDimensions;return{reference:Um(t,await s(n),o),floating:{x:0,y:0,...await r(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>at(e).direction==="rtl"},Bm=(e,t,n)=>{const o=new Map,s={platform:Fm,...n},r={...s.platform,_c:o};return Dm(e,t,{...s,platform:r})},tn={disabled:!1,distance:5,skidding:0,container:"body",boundary:void 0,instantMove:!1,disposeTimeout:150,popperTriggers:[],strategy:"absolute",preventOverflow:!0,flip:!0,shift:!0,overflowPadding:0,arrowPadding:0,arrowOverflow:!0,autoHideOnMousedown:!1,themes:{tooltip:{placement:"top",triggers:["hover","focus","touch"],hideTriggers:e=>[...e,"click"],delay:{show:200,hide:0},handleResize:!1,html:!1,loadingContent:"..."},dropdown:{placement:"bottom",triggers:["click"],delay:0,handleResize:!0,autoHide:!0},menu:{$extend:"dropdown",triggers:["hover","focus"],popperTriggers:["hover"],delay:{show:0,hide:400}}}};function Fr(e,t){let n=tn.themes[e]||{},o;do o=n[t],typeof o>"u"?n.$extend?n=tn.themes[n.$extend]||{}:(n=null,o=tn[t]):n=null;while(n);return o}function Hm(e){const t=[e];let n=tn.themes[e]||{};do n.$extend&&!n.$resetCss?(t.push(n.$extend),n=tn.themes[n.$extend]||{}):n=null;while(n);return t.map(o=>`v-popper--theme-${o}`)}function Wa(e){const t=[e];let n=tn.themes[e]||{};do n.$extend?(t.push(n.$extend),n=tn.themes[n.$extend]||{}):n=null;while(n);return t}let oo=!1;if(typeof window<"u"){oo=!1;try{const e=Object.defineProperty({},"passive",{get(){oo=!0}});window.addEventListener("test",null,e)}catch{}}let Ga=!1;typeof window<"u"&&typeof navigator<"u"&&(Ga=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);const zm=["auto","top","bottom","left","right"].reduce((e,t)=>e.concat([t,`${t}-start`,`${t}-end`]),[]),Ya={hover:"mouseenter",focus:"focus",click:"click",touch:"touchstart",pointer:"pointerdown"},qa={hover:"mouseleave",focus:"blur",click:"click",touch:"touchend",pointer:"pointerup"};function Xa(e,t){const n=e.indexOf(t);n!==-1&&e.splice(n,1)}function Br(){return new Promise(e=>requestAnimationFrame(()=>{requestAnimationFrame(e)}))}const Je=[];let nn=null;const Za={};function Ja(e){let t=Za[e];return t||(t=Za[e]=[]),t}let Hr=function(){};typeof window<"u"&&(Hr=window.Element);function ee(e){return function(t){return Fr(t.theme,e)}}const zr="__floating-vue__popper",Qa=()=>cn({name:"VPopper",provide(){return{[zr]:{parentPopper:this}}},inject:{[zr]:{default:null}},props:{theme:{type:String,required:!0},targetNodes:{type:Function,required:!0},referenceNode:{type:Function,default:null},popperNode:{type:Function,required:!0},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:ee("disabled")},positioningDisabled:{type:Boolean,default:ee("positioningDisabled")},placement:{type:String,default:ee("placement"),validator:e=>zm.includes(e)},delay:{type:[String,Number,Object],default:ee("delay")},distance:{type:[Number,String],default:ee("distance")},skidding:{type:[Number,String],default:ee("skidding")},triggers:{type:Array,default:ee("triggers")},showTriggers:{type:[Array,Function],default:ee("showTriggers")},hideTriggers:{type:[Array,Function],default:ee("hideTriggers")},popperTriggers:{type:Array,default:ee("popperTriggers")},popperShowTriggers:{type:[Array,Function],default:ee("popperShowTriggers")},popperHideTriggers:{type:[Array,Function],default:ee("popperHideTriggers")},container:{type:[String,Object,Hr,Boolean],default:ee("container")},boundary:{type:[String,Hr],default:ee("boundary")},strategy:{type:String,validator:e=>["absolute","fixed"].includes(e),default:ee("strategy")},autoHide:{type:[Boolean,Function],default:ee("autoHide")},handleResize:{type:Boolean,default:ee("handleResize")},instantMove:{type:Boolean,default:ee("instantMove")},eagerMount:{type:Boolean,default:ee("eagerMount")},popperClass:{type:[String,Array,Object],default:ee("popperClass")},computeTransformOrigin:{type:Boolean,default:ee("computeTransformOrigin")},autoMinSize:{type:Boolean,default:ee("autoMinSize")},autoSize:{type:[Boolean,String],default:ee("autoSize")},autoMaxSize:{type:Boolean,default:ee("autoMaxSize")},autoBoundaryMaxSize:{type:Boolean,default:ee("autoBoundaryMaxSize")},preventOverflow:{type:Boolean,default:ee("preventOverflow")},overflowPadding:{type:[Number,String],default:ee("overflowPadding")},arrowPadding:{type:[Number,String],default:ee("arrowPadding")},arrowOverflow:{type:Boolean,default:ee("arrowOverflow")},flip:{type:Boolean,default:ee("flip")},shift:{type:Boolean,default:ee("shift")},shiftCrossAxis:{type:Boolean,default:ee("shiftCrossAxis")},noAutoFocus:{type:Boolean,default:ee("noAutoFocus")},disposeTimeout:{type:Number,default:ee("disposeTimeout")}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},data(){return{isShown:!1,isMounted:!1,skipTransition:!1,classes:{showFrom:!1,showTo:!1,hideFrom:!1,hideTo:!0},result:{x:0,y:0,placement:"",strategy:this.strategy,arrow:{x:0,y:0,centerOffset:0},transformOrigin:null},randomId:`popper_${[Math.random(),Date.now()].map(e=>e.toString(36).substring(2,10)).join("_")}`,shownChildren:new Set,lastAutoHide:!0,pendingHide:!1,containsGlobalTarget:!1,isDisposed:!0,mouseDownContains:!1}},computed:{popperId(){return this.ariaId!=null?this.ariaId:this.randomId},shouldMountContent(){return this.eagerMount||this.isMounted},slotData(){return{popperId:this.popperId,isShown:this.isShown,shouldMountContent:this.shouldMountContent,skipTransition:this.skipTransition,autoHide:typeof this.autoHide=="function"?this.lastAutoHide:this.autoHide,show:this.show,hide:this.hide,handleResize:this.handleResize,onResize:this.onResize,classes:{...this.classes,popperClass:this.popperClass},result:this.positioningDisabled?null:this.result,attrs:this.$attrs}},parentPopper(){var e;return(e=this[zr])==null?void 0:e.parentPopper},hasPopperShowTriggerHover(){var e,t;return((e=this.popperTriggers)==null?void 0:e.includes("hover"))||((t=this.popperShowTriggers)==null?void 0:t.includes("hover"))}},watch:{shown:"$_autoShowHide",disabled(e){e?this.dispose():this.init()},async container(){this.isShown&&(this.$_ensureTeleport(),await this.$_computePosition())},triggers:{handler:"$_refreshListeners",deep:!0},positioningDisabled:"$_refreshListeners",...["placement","distance","skidding","boundary","strategy","overflowPadding","arrowPadding","preventOverflow","shift","shiftCrossAxis","flip"].reduce((e,t)=>(e[t]="$_computePosition",e),{})},created(){this.autoMinSize&&console.warn('[floating-vue] `autoMinSize` option is deprecated. Use `autoSize="min"` instead.'),this.autoMaxSize&&console.warn("[floating-vue] `autoMaxSize` option is deprecated. Use `autoBoundaryMaxSize` instead.")},mounted(){this.init(),this.$_detachPopperNode()},activated(){this.$_autoShowHide()},deactivated(){this.hide()},beforeUnmount(){this.dispose()},methods:{show({event:e=null,skipDelay:t=!1,force:n=!1}={}){var o,s;(o=this.parentPopper)!=null&&o.lockedChild&&this.parentPopper.lockedChild!==this||(this.pendingHide=!1,(n||!this.disabled)&&(((s=this.parentPopper)==null?void 0:s.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.$_scheduleShow(e,t),this.$emit("show"),this.$_showFrameLocked=!0,requestAnimationFrame(()=>{this.$_showFrameLocked=!1})),this.$emit("update:shown",!0))},hide({event:e=null,skipDelay:t=!1}={}){var n;if(!this.$_hideInProgress){if(this.shownChildren.size>0){this.pendingHide=!0;return}if(this.hasPopperShowTriggerHover&&this.$_isAimingPopper()){this.parentPopper&&(this.parentPopper.lockedChild=this,clearTimeout(this.parentPopper.lockedChildTimer),this.parentPopper.lockedChildTimer=setTimeout(()=>{this.parentPopper.lockedChild===this&&(this.parentPopper.lockedChild.hide({skipDelay:t}),this.parentPopper.lockedChild=null)},1e3));return}((n=this.parentPopper)==null?void 0:n.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.pendingHide=!1,this.$_scheduleHide(e,t),this.$emit("hide"),this.$emit("update:shown",!1)}},init(){var e;this.isDisposed&&(this.isDisposed=!1,this.isMounted=!1,this.$_events=[],this.$_preventShow=!1,this.$_referenceNode=((e=this.referenceNode)==null?void 0:e.call(this))??this.$el,this.$_targetNodes=this.targetNodes().filter(t=>t.nodeType===t.ELEMENT_NODE),this.$_popperNode=this.popperNode(),this.$_innerNode=this.$_popperNode.querySelector(".v-popper__inner"),this.$_arrowNode=this.$_popperNode.querySelector(".v-popper__arrow-container"),this.$_swapTargetAttrs("title","data-original-title"),this.$_detachPopperNode(),this.triggers.length&&this.$_addEventListeners(),this.shown&&this.show())},dispose(){this.isDisposed||(this.isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.$_detachPopperNode(),this.isMounted=!1,this.isShown=!1,this.$_updateParentShownChildren(!1),this.$_swapTargetAttrs("data-original-title","title"))},async onResize(){this.isShown&&(await this.$_computePosition(),this.$emit("resize"))},async $_computePosition(){if(this.isDisposed||this.positioningDisabled)return;const e={strategy:this.strategy,middleware:[]};(this.distance||this.skidding)&&e.middleware.push(Vm({mainAxis:this.distance,crossAxis:this.skidding}));const t=this.placement.startsWith("auto");if(t?e.middleware.push(Rm({alignment:this.placement.split("-")[1]??""})):e.placement=this.placement,this.preventOverflow&&(this.shift&&e.middleware.push(Lm({padding:this.overflowPadding,boundary:this.boundary,crossAxis:this.shiftCrossAxis})),!t&&this.flip&&e.middleware.push(km({padding:this.overflowPadding,boundary:this.boundary}))),e.middleware.push(Pm({element:this.$_arrowNode,padding:this.arrowPadding})),this.arrowOverflow&&e.middleware.push({name:"arrowOverflow",fn:({placement:o,rects:s,middlewareData:r})=>{let i;const{centerOffset:l}=r.arrow;return o.startsWith("top")||o.startsWith("bottom")?i=Math.abs(l)>s.reference.width/2:i=Math.abs(l)>s.reference.height/2,{data:{overflow:i}}}}),this.autoMinSize||this.autoSize){const o=this.autoSize?this.autoSize:this.autoMinSize?"min":null;e.middleware.push({name:"autoSize",fn:({rects:s,placement:r,middlewareData:i})=>{var l;if((l=i.autoSize)!=null&&l.skip)return{};let u,a;return r.startsWith("top")||r.startsWith("bottom")?u=s.reference.width:a=s.reference.height,this.$_innerNode.style[o==="min"?"minWidth":o==="max"?"maxWidth":"width"]=u!=null?`${u}px`:null,this.$_innerNode.style[o==="min"?"minHeight":o==="max"?"maxHeight":"height"]=a!=null?`${a}px`:null,{data:{skip:!0},reset:{rects:!0}}}})}(this.autoMaxSize||this.autoBoundaryMaxSize)&&(this.$_innerNode.style.maxWidth=null,this.$_innerNode.style.maxHeight=null,e.middleware.push(Mm({boundary:this.boundary,padding:this.overflowPadding,apply:({availableWidth:o,availableHeight:s})=>{this.$_innerNode.style.maxWidth=o!=null?`${o}px`:null,this.$_innerNode.style.maxHeight=s!=null?`${s}px`:null}})));const n=await Bm(this.$_referenceNode,this.$_popperNode,e);Object.assign(this.result,{x:n.x,y:n.y,placement:n.placement,strategy:n.strategy,arrow:{...n.middlewareData.arrow,...n.middlewareData.arrowOverflow}})},$_scheduleShow(e,t=!1){if(this.$_updateParentShownChildren(!0),this.$_hideInProgress=!1,clearTimeout(this.$_scheduleTimer),nn&&this.instantMove&&nn.instantMove&&nn!==this.parentPopper){nn.$_applyHide(!0),this.$_applyShow(!0);return}t?this.$_applyShow():this.$_scheduleTimer=setTimeout(this.$_applyShow.bind(this),this.$_computeDelay("show"))},$_scheduleHide(e,t=!1){if(this.shownChildren.size>0){this.pendingHide=!0;return}this.$_updateParentShownChildren(!1),this.$_hideInProgress=!0,clearTimeout(this.$_scheduleTimer),this.isShown&&(nn=this),t?this.$_applyHide():this.$_scheduleTimer=setTimeout(this.$_applyHide.bind(this),this.$_computeDelay("hide"))},$_computeDelay(e){const t=this.delay;return parseInt(t&&t[e]||t||0)},async $_applyShow(e=!1){clearTimeout(this.$_disposeTimer),clearTimeout(this.$_scheduleTimer),this.skipTransition=e,!this.isShown&&(this.$_ensureTeleport(),await Br(),await this.$_computePosition(),await this.$_applyShowEffect(),this.positioningDisabled||this.$_registerEventListeners([...os(this.$_referenceNode),...os(this.$_popperNode)],"scroll",()=>{this.$_computePosition()}))},async $_applyShowEffect(){if(this.$_hideInProgress)return;if(this.computeTransformOrigin){const t=this.$_referenceNode.getBoundingClientRect(),n=this.$_popperNode.querySelector(".v-popper__wrapper"),o=n.parentNode.getBoundingClientRect(),s=t.x+t.width/2-(o.left+n.offsetLeft),r=t.y+t.height/2-(o.top+n.offsetTop);this.result.transformOrigin=`${s}px ${r}px`}this.isShown=!0,this.$_applyAttrsToTarget({"aria-describedby":this.popperId,"data-popper-shown":""});const e=this.showGroup;if(e){let t;for(let n=0;n<Je.length;n++)t=Je[n],t.showGroup!==e&&(t.hide(),t.$emit("close-group"))}Je.push(this),document.body.classList.add("v-popper--some-open");for(const t of Wa(this.theme))Ja(t).push(this),document.body.classList.add(`v-popper--some-open--${t}`);this.$emit("apply-show"),this.classes.showFrom=!0,this.classes.showTo=!1,this.classes.hideFrom=!1,this.classes.hideTo=!1,await Br(),this.classes.showFrom=!1,this.classes.showTo=!0,this.noAutoFocus||this.$_popperNode.focus()},async $_applyHide(e=!1){if(this.shownChildren.size>0){this.pendingHide=!0,this.$_hideInProgress=!1;return}if(clearTimeout(this.$_scheduleTimer),!this.isShown)return;this.skipTransition=e,Xa(Je,this),Je.length===0&&document.body.classList.remove("v-popper--some-open");for(const n of Wa(this.theme)){const o=Ja(n);Xa(o,this),o.length===0&&document.body.classList.remove(`v-popper--some-open--${n}`)}nn===this&&(nn=null),this.isShown=!1,this.$_applyAttrsToTarget({"aria-describedby":void 0,"data-popper-shown":void 0}),clearTimeout(this.$_disposeTimer);const t=this.disposeTimeout;t!==null&&(this.$_disposeTimer=setTimeout(()=>{this.$_popperNode&&(this.$_detachPopperNode(),this.isMounted=!1)},t)),this.$_removeEventListeners("scroll"),this.$emit("apply-hide"),this.classes.showFrom=!1,this.classes.showTo=!1,this.classes.hideFrom=!0,this.classes.hideTo=!1,await Br(),this.classes.hideFrom=!1,this.classes.hideTo=!0},$_autoShowHide(){this.shown?this.show():this.hide()},$_ensureTeleport(){if(this.isDisposed)return;let e=this.container;if(typeof e=="string"?e=window.document.querySelector(e):e===!1&&(e=this.$_targetNodes[0].parentNode),!e)throw new Error("No container for popover: "+this.container);e.appendChild(this.$_popperNode),this.isMounted=!0},$_addEventListeners(){const e=n=>{this.isShown&&!this.$_hideInProgress||(n.usedByTooltip=!0,!this.$_preventShow&&this.show({event:n}))};this.$_registerTriggerListeners(this.$_targetNodes,Ya,this.triggers,this.showTriggers,e),this.$_registerTriggerListeners([this.$_popperNode],Ya,this.popperTriggers,this.popperShowTriggers,e);const t=n=>{n.usedByTooltip||this.hide({event:n})};this.$_registerTriggerListeners(this.$_targetNodes,qa,this.triggers,this.hideTriggers,t),this.$_registerTriggerListeners([this.$_popperNode],qa,this.popperTriggers,this.popperHideTriggers,t)},$_registerEventListeners(e,t,n){this.$_events.push({targetNodes:e,eventType:t,handler:n}),e.forEach(o=>o.addEventListener(t,n,oo?{passive:!0}:void 0))},$_registerTriggerListeners(e,t,n,o,s){let r=n;o!=null&&(r=typeof o=="function"?o(r):o),r.forEach(i=>{const l=t[i];l&&this.$_registerEventListeners(e,l,s)})},$_removeEventListeners(e){const t=[];this.$_events.forEach(n=>{const{targetNodes:o,eventType:s,handler:r}=n;!e||e===s?o.forEach(i=>i.removeEventListener(s,r)):t.push(n)}),this.$_events=t},$_refreshListeners(){this.isDisposed||(this.$_removeEventListeners(),this.$_addEventListeners())},$_handleGlobalClose(e,t=!1){this.$_showFrameLocked||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),t&&(this.$_preventShow=!0,setTimeout(()=>{this.$_preventShow=!1},300)))},$_detachPopperNode(){this.$_popperNode.parentNode&&this.$_popperNode.parentNode.removeChild(this.$_popperNode)},$_swapTargetAttrs(e,t){for(const n of this.$_targetNodes){const o=n.getAttribute(e);o&&(n.removeAttribute(e),n.setAttribute(t,o))}},$_applyAttrsToTarget(e){for(const t of this.$_targetNodes)for(const n in e){const o=e[n];o==null?t.removeAttribute(n):t.setAttribute(n,o)}},$_updateParentShownChildren(e){let t=this.parentPopper;for(;t;)e?t.shownChildren.add(this.randomId):(t.shownChildren.delete(this.randomId),t.pendingHide&&t.hide()),t=t.parentPopper},$_isAimingPopper(){const e=this.$_referenceNode.getBoundingClientRect();if(so>=e.left&&so<=e.right&&ro>=e.top&&ro<=e.bottom){const t=this.$_popperNode.getBoundingClientRect(),n=so-Ut,o=ro-Ft,s=t.left+t.width/2-Ut+(t.top+t.height/2)-Ft+t.width+t.height,r=Ut+n*s,i=Ft+o*s;return ss(Ut,Ft,r,i,t.left,t.top,t.left,t.bottom)||ss(Ut,Ft,r,i,t.left,t.top,t.right,t.top)||ss(Ut,Ft,r,i,t.right,t.top,t.right,t.bottom)||ss(Ut,Ft,r,i,t.left,t.bottom,t.right,t.bottom)}return!1}},render(){return this.$slots.default(this.slotData)}});if(typeof document<"u"&&typeof window<"u"){if(Ga){const e=oo?{passive:!0,capture:!0}:!0;document.addEventListener("touchstart",t=>ec(t),e),document.addEventListener("touchend",t=>tc(t,!0),e)}else window.addEventListener("mousedown",e=>ec(e),!0),window.addEventListener("click",e=>tc(e,!1),!0);window.addEventListener("resize",Wm)}function ec(e,t){for(let n=0;n<Je.length;n++){const o=Je[n];try{o.mouseDownContains=o.popperNode().contains(e.target)}catch{}}}function tc(e,t){jm(e,t)}function jm(e,t){const n={};for(let o=Je.length-1;o>=0;o--){const s=Je[o];try{const r=s.containsGlobalTarget=s.mouseDownContains||s.popperNode().contains(e.target);s.pendingHide=!1,requestAnimationFrame(()=>{if(s.pendingHide=!1,!n[s.randomId]&&nc(s,r,e)){if(s.$_handleGlobalClose(e,t),!e.closeAllPopover&&e.closePopover&&r){let l=s.parentPopper;for(;l;)n[l.randomId]=!0,l=l.parentPopper;return}let i=s.parentPopper;for(;i&&nc(i,i.containsGlobalTarget,e);)i.$_handleGlobalClose(e,t),i=i.parentPopper}})}catch{}}}function nc(e,t,n){return n.closeAllPopover||n.closePopover&&t||Km(e,n)&&!t}function Km(e,t){if(typeof e.autoHide=="function"){const n=e.autoHide(t);return e.lastAutoHide=n,n}return e.autoHide}function Wm(){for(let e=0;e<Je.length;e++)Je[e].$_computePosition()}let Ut=0,Ft=0,so=0,ro=0;typeof window<"u"&&window.addEventListener("mousemove",e=>{Ut=so,Ft=ro,so=e.clientX,ro=e.clientY},oo?{passive:!0}:void 0);function ss(e,t,n,o,s,r,i,l){const u=((i-s)*(t-r)-(l-r)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t)),a=((n-e)*(t-r)-(o-t)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t));return u>=0&&u<=1&&a>=0&&a<=1}const Gm={extends:Qa()},jr=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n};function Ym(e,t,n,o,s,r){return Re(),Ct("div",{ref:"reference",class:ht(["v-popper",{"v-popper--shown":e.slotData.isShown}])},[ko(e.$slots,"default",Uc(fl(e.slotData)))],2)}const qm=jr(Gm,[["render",Ym]]);function Xm(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var o=e.indexOf("rv:");return parseInt(e.substring(o+3,e.indexOf(".",o)),10)}var s=e.indexOf("Edge/");return s>0?parseInt(e.substring(s+5,e.indexOf(".",s)),10):-1}let rs;function Kr(){Kr.init||(Kr.init=!0,rs=Xm()!==-1)}var is={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){Kr(),Ao(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",rs&&this.$el.appendChild(e),e.data="about:blank",rs||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!rs&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const Zm=Nf();Rf("data-v-b329ee4c");const Jm={class:"resize-observer",tabindex:"-1"};kf();const Qm=Zm((e,t,n,o,s,r)=>(Re(),dn("div",Jm)));is.render=Qm,is.__scopeId="data-v-b329ee4c",is.__file="src/components/ResizeObserver.vue";const oc=(e="theme")=>({computed:{themeClass(){return Hm(this[e])}}}),eg=cn({name:"VPopperContent",components:{ResizeObserver:is},mixins:[oc()],props:{popperId:String,theme:String,shown:Boolean,mounted:Boolean,skipTransition:Boolean,autoHide:Boolean,handleResize:Boolean,classes:Object,result:Object},emits:["hide","resize"],methods:{toPx(e){return e!=null&&!isNaN(e)?`${e}px`:null}}}),tg=["id","aria-hidden","tabindex","data-popper-placement"],ng={ref:"inner",class:"v-popper__inner"},og=ie("div",{class:"v-popper__arrow-outer"},null,-1),sg=ie("div",{class:"v-popper__arrow-inner"},null,-1),rg=[og,sg];function ig(e,t,n,o,s,r){const i=Bs("ResizeObserver");return Re(),Ct("div",{id:e.popperId,ref:"popover",class:ht(["v-popper__popper",[e.themeClass,e.classes.popperClass,{"v-popper__popper--shown":e.shown,"v-popper__popper--hidden":!e.shown,"v-popper__popper--show-from":e.classes.showFrom,"v-popper__popper--show-to":e.classes.showTo,"v-popper__popper--hide-from":e.classes.hideFrom,"v-popper__popper--hide-to":e.classes.hideTo,"v-popper__popper--skip-transition":e.skipTransition,"v-popper__popper--arrow-overflow":e.result&&e.result.arrow.overflow,"v-popper__popper--no-positioning":!e.result}]]),style:Ne(e.result?{position:e.result.strategy,transform:`translate3d(${Math.round(e.result.x)}px,${Math.round(e.result.y)}px,0)`}:void 0),"aria-hidden":e.shown?"false":"true",tabindex:e.autoHide?0:void 0,"data-popper-placement":e.result?e.result.placement:void 0,onKeyup:t[2]||(t[2]=ap(l=>e.autoHide&&e.$emit("hide"),["esc"]))},[ie("div",{class:"v-popper__backdrop",onClick:t[0]||(t[0]=l=>e.autoHide&&e.$emit("hide"))}),ie("div",{class:"v-popper__wrapper",style:Ne(e.result?{transformOrigin:e.result.transformOrigin}:void 0)},[ie("div",ng,[e.mounted?(Re(),Ct(Ie,{key:0},[ie("div",null,[ko(e.$slots,"default")]),e.handleResize?(Re(),dn(i,{key:0,onNotify:t[1]||(t[1]=l=>e.$emit("resize",l))})):$o("",!0)],64)):$o("",!0)],512),ie("div",{ref:"arrow",class:"v-popper__arrow-container",style:Ne(e.result?{left:e.toPx(e.result.arrow.x),top:e.toPx(e.result.arrow.y)}:void 0)},rg,4)],4)],46,tg)}const sc=jr(eg,[["render",ig]]),rc={methods:{show(...e){return this.$refs.popper.show(...e)},hide(...e){return this.$refs.popper.hide(...e)},dispose(...e){return this.$refs.popper.dispose(...e)},onResize(...e){return this.$refs.popper.onResize(...e)}}};let Wr=function(){};typeof window<"u"&&(Wr=window.Element);const lg=cn({name:"VPopperWrapper",components:{Popper:qm,PopperContent:sc},mixins:[rc,oc("finalTheme")],props:{theme:{type:String,default:null},referenceNode:{type:Function,default:null},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:void 0},positioningDisabled:{type:Boolean,default:void 0},placement:{type:String,default:void 0},delay:{type:[String,Number,Object],default:void 0},distance:{type:[Number,String],default:void 0},skidding:{type:[Number,String],default:void 0},triggers:{type:Array,default:void 0},showTriggers:{type:[Array,Function],default:void 0},hideTriggers:{type:[Array,Function],default:void 0},popperTriggers:{type:Array,default:void 0},popperShowTriggers:{type:[Array,Function],default:void 0},popperHideTriggers:{type:[Array,Function],default:void 0},container:{type:[String,Object,Wr,Boolean],default:void 0},boundary:{type:[String,Wr],default:void 0},strategy:{type:String,default:void 0},autoHide:{type:[Boolean,Function],default:void 0},handleResize:{type:Boolean,default:void 0},instantMove:{type:Boolean,default:void 0},eagerMount:{type:Boolean,default:void 0},popperClass:{type:[String,Array,Object],default:void 0},computeTransformOrigin:{type:Boolean,default:void 0},autoMinSize:{type:Boolean,default:void 0},autoSize:{type:[Boolean,String],default:void 0},autoMaxSize:{type:Boolean,default:void 0},autoBoundaryMaxSize:{type:Boolean,default:void 0},preventOverflow:{type:Boolean,default:void 0},overflowPadding:{type:[Number,String],default:void 0},arrowPadding:{type:[Number,String],default:void 0},arrowOverflow:{type:Boolean,default:void 0},flip:{type:Boolean,default:void 0},shift:{type:Boolean,default:void 0},shiftCrossAxis:{type:Boolean,default:void 0},noAutoFocus:{type:Boolean,default:void 0},disposeTimeout:{type:Number,default:void 0}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},computed:{finalTheme(){return this.theme??this.$options.vPopperTheme}},methods:{getTargetNodes(){return Array.from(this.$el.children).filter(e=>e!==this.$refs.popperContent.$el)}}});function ug(e,t,n,o,s,r){const i=Bs("PopperContent"),l=Bs("Popper");return Re(),dn(l,dl({ref:"popper"},e.$props,{theme:e.finalTheme,"target-nodes":e.getTargetNodes,"popper-node":()=>e.$refs.popperContent.$el,class:[e.themeClass],onShow:t[0]||(t[0]=()=>e.$emit("show")),onHide:t[1]||(t[1]=()=>e.$emit("hide")),"onUpdate:shown":t[2]||(t[2]=u=>e.$emit("update:shown",u)),onApplyShow:t[3]||(t[3]=()=>e.$emit("apply-show")),onApplyHide:t[4]||(t[4]=()=>e.$emit("apply-hide")),onCloseGroup:t[5]||(t[5]=()=>e.$emit("close-group")),onCloseDirective:t[6]||(t[6]=()=>e.$emit("close-directive")),onAutoHide:t[7]||(t[7]=()=>e.$emit("auto-hide")),onResize:t[8]||(t[8]=()=>e.$emit("resize"))}),{default:Po(({popperId:u,isShown:a,shouldMountContent:c,skipTransition:f,autoHide:h,show:d,hide:_,handleResize:v,onResize:y,classes:g,result:x})=>[ko(e.$slots,"default",{shown:a,show:d,hide:_}),Se(i,{ref:"popperContent","popper-id":u,theme:e.finalTheme,shown:a,mounted:c,"skip-transition":f,"auto-hide":h,"handle-resize":v,classes:g,result:x,onHide:_,onResize:y},{default:Po(()=>[ko(e.$slots,"popper",{shown:a,hide:_})]),_:2},1032,["popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:3},16,["theme","target-nodes","popper-node","class"])}const Gr=jr(lg,[["render",ug]]);({...Gr},{...Gr}),{...Gr},Qa();function ic(e){return oi()?(zc(e),!0):!1}const Yr=new WeakMap,ag=(...e)=>{var t;const n=e[0],o=(t=Qs())==null?void 0:t.proxy;if(o==null&&!zi())throw new Error("injectLocal must be called in setup");return o&&Yr.has(o)&&n in Yr.get(o)?Yr.get(o)[n]:Vn(...e)},lc=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const cg=e=>e!=null,fg=Object.prototype.toString,dg=e=>fg.call(e)==="[object Object]",qr=()=>{};function uc(e,t){function n(...o){return new Promise((s,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(r)})}return n}const ac=e=>e();function pg(e,t={}){let n,o,s=qr;const r=u=>{clearTimeout(u),s(),s=qr};let i;return u=>{const a=Ae(e),c=Ae(t.maxWait);return n&&r(n),a<=0||c!==void 0&&c<=0?(o&&(r(o),o=null),Promise.resolve(u())):new Promise((f,h)=>{s=t.rejectOnCancel?h:f,i=u,c&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,f(i())},c)),n=setTimeout(()=>{o&&r(o),o=null,f(u())},a)})}}function hg(e=ac,t={}){const{initialState:n="active"}=t,o=fc(n==="active");function s(){o.value=!1}function r(){o.value=!0}const i=(...l)=>{o.value&&e(...l)};return{isActive:Cn(o),pause:s,resume:r,eventFilter:i}}function cc(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function _g(e){return Qs()}function ls(e){return Array.isArray(e)?e:[e]}function fc(...e){if(e.length!==1)return vf(...e);const t=e[0];return typeof t=="function"?Cn(_f(()=>({get:t,set:qr}))):Ve(t)}function mg(e,t=200,n={}){return uc(pg(t,n),e)}function gg(e,t,n={}){const{eventFilter:o=ac,...s}=n;return Ye(e,uc(o,t),s)}function vg(e,t,n={}){const{eventFilter:o,initialState:s="active",...r}=n,{eventFilter:i,pause:l,resume:u,isActive:a}=hg(o,{initialState:s});return{stop:gg(e,t,{...r,eventFilter:i}),pause:l,resume:u,isActive:a}}function Xr(e,t=!0,n){_g()?Rn(e,n):t?e():Ao(e)}function yg(e,t,n){return Ye(e,t,{...n,immediate:!0})}const wt=lc?window:void 0;function us(e){var t;const n=Ae(e);return(t=n==null?void 0:n.$el)!=null?t:n}function Pe(...e){const t=[],n=()=>{t.forEach(l=>l()),t.length=0},o=(l,u,a,c)=>(l.addEventListener(u,a,c),()=>l.removeEventListener(u,a,c)),s=_e(()=>{const l=ls(Ae(e[0])).filter(u=>u!=null);return l.every(u=>typeof u!="string")?l:void 0}),r=yg(()=>{var l,u;return[(u=(l=s.value)==null?void 0:l.map(a=>us(a)))!=null?u:[wt].filter(a=>a!=null),ls(Ae(s.value?e[1]:e[0])),ls(J(s.value?e[2]:e[1])),Ae(s.value?e[3]:e[2])]},([l,u,a,c])=>{if(n(),!(l!=null&&l.length)||!(u!=null&&u.length)||!(a!=null&&a.length))return;const f=dg(c)?{...c}:c;t.push(...l.flatMap(h=>u.flatMap(d=>a.map(_=>o(h,d,_,f)))))},{flush:"post"}),i=()=>{r(),n()};return ic(n),i}function Eg(){const e=Le(!1),t=Qs();return t&&Rn(()=>{e.value=!0},t),e}function dc(e){const t=Eg();return _e(()=>(t.value,!!e()))}function bg(e,t,n={}){const{window:o=wt,...s}=n;let r;const i=dc(()=>o&&"MutationObserver"in o),l=()=>{r&&(r.disconnect(),r=void 0)},u=_e(()=>{const h=Ae(e),d=ls(h).map(us).filter(cg);return new Set(d)}),a=Ye(()=>u.value,h=>{l(),i.value&&h.size&&(r=new MutationObserver(t),h.forEach(d=>r.observe(d,s)))},{immediate:!0,flush:"post"}),c=()=>r==null?void 0:r.takeRecords(),f=()=>{a(),l()};return ic(f),{isSupported:i,stop:f,takeRecords:c}}const wg=Symbol("vueuse-ssr-width");function Sg(){const e=zi()?ag(wg,null):null;return typeof e=="number"?e:void 0}function pc(e,t={}){const{window:n=wt,ssrWidth:o=Sg()}=t,s=dc(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),r=Le(typeof o=="number"),i=Le(),l=Le(!1),u=a=>{l.value=a.matches};return qs(()=>{if(r.value){r.value=!s.value;const a=Ae(e).split(",");l.value=a.some(c=>{const f=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),d=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let _=!!(h||d);return h&&_&&(_=o>=cc(h[1])),d&&_&&(_=o<=cc(d[1])),f?!_:_});return}s.value&&(i.value=n.matchMedia(Ae(e)),l.value=i.value.matches)}),Pe(i,"change",u,{passive:!0}),_e(()=>l.value)}const as=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},cs="__vueuse_ssr_handlers__",xg=Tg();function Tg(){return cs in as||(as[cs]=as[cs]||{}),as[cs]}function hc(e,t){return xg[e]||t}function Og(e){return pc("(prefers-color-scheme: dark)",e)}function Ag(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Cg={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},_c="vueuse-storage";function mc(e,t,n,o={}){var s;const{flush:r="pre",deep:i=!0,listenToStorageChanges:l=!0,writeDefaults:u=!0,mergeDefaults:a=!1,shallow:c,window:f=wt,eventFilter:h,onError:d=R=>{console.error(R)},initOnMounted:_}=o,v=(c?Le:Ve)(typeof t=="function"?t():t),y=_e(()=>Ae(e));if(!n)try{n=hc("getDefaultStorage",()=>{var R;return(R=wt)==null?void 0:R.localStorage})()}catch(R){d(R)}if(!n)return v;const g=Ae(t),x=Ag(g),D=(s=o.serializer)!=null?s:Cg[x],{pause:b,resume:P}=vg(v,()=>H(v.value),{flush:r,deep:i,eventFilter:h});Ye(y,()=>$(),{flush:r}),f&&l&&Xr(()=>{n instanceof Storage?Pe(f,"storage",$,{passive:!0}):Pe(f,_c,I),_&&$()}),_||$();function U(R,F){if(f){const z={key:y.value,oldValue:R,newValue:F,storageArea:n};f.dispatchEvent(n instanceof Storage?new StorageEvent("storage",z):new CustomEvent(_c,{detail:z}))}}function H(R){try{const F=n.getItem(y.value);if(R==null)U(F,null),n.removeItem(y.value);else{const z=D.write(R);F!==z&&(n.setItem(y.value,z),U(F,z))}}catch(F){d(F)}}function W(R){const F=R?R.newValue:n.getItem(y.value);if(F==null)return u&&g!=null&&n.setItem(y.value,D.write(g)),g;if(!R&&a){const z=D.read(F);return typeof a=="function"?a(z,g):x==="object"&&!Array.isArray(z)?{...g,...z}:z}else return typeof F!="string"?F:D.read(F)}function $(R){if(!(R&&R.storageArea!==n)){if(R&&R.key==null){v.value=g;return}if(!(R&&R.key!==y.value)){b();try{(R==null?void 0:R.newValue)!==D.write(v.value)&&(v.value=W(R))}catch(F){d(F)}finally{R?Ao(P):P()}}}}function I(R){$(R.detail)}return v}const Dg="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Pg(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:s=wt,storage:r,storageKey:i="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:u,emitAuto:a,disableTransition:c=!0}=e,f={auto:"",light:"light",dark:"dark",...e.modes||{}},h=Og({window:s}),d=_e(()=>h.value?"dark":"light"),_=u||(i==null?fc(o):mc(i,o,r,{window:s,listenToStorageChanges:l})),v=_e(()=>_.value==="auto"?d.value:_.value),y=hc("updateHTMLAttrs",(b,P,U)=>{const H=typeof b=="string"?s==null?void 0:s.document.querySelector(b):us(b);if(!H)return;const W=new Set,$=new Set;let I=null;if(P==="class"){const F=U.split(/\s/g);Object.values(f).flatMap(z=>(z||"").split(/\s/g)).filter(Boolean).forEach(z=>{F.includes(z)?W.add(z):$.add(z)})}else I={key:P,value:U};if(W.size===0&&$.size===0&&I===null)return;let R;c&&(R=s.document.createElement("style"),R.appendChild(document.createTextNode(Dg)),s.document.head.appendChild(R));for(const F of W)H.classList.add(F);for(const F of $)H.classList.remove(F);I&&H.setAttribute(I.key,I.value),c&&(s.getComputedStyle(R).opacity,document.head.removeChild(R))});function g(b){var P;y(t,n,(P=f[b])!=null?P:b)}function x(b){e.onChanged?e.onChanged(b,g):g(b)}Ye(v,x,{flush:"post",immediate:!0}),Xr(()=>x(v.value));const D=_e({get(){return a?_.value:v.value},set(b){_.value=b}});return Object.assign(D,{store:_,system:d,state:v})}function fs(e,t,n={}){const{window:o=wt,initialValue:s,observe:r=!1}=n,i=Le(s),l=_e(()=>{var a;return us(t)||((a=o==null?void 0:o.document)==null?void 0:a.documentElement)});function u(){var a;const c=Ae(e),f=Ae(l);if(f&&o&&c){const h=(a=o.getComputedStyle(f).getPropertyValue(c))==null?void 0:a.trim();i.value=h||i.value||s}}return r&&bg(l,u,{attributeFilter:["style","class"],window:o}),Ye([l,()=>Ae(e)],(a,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()},{immediate:!0}),Ye([i,l],([a,c])=>{const f=Ae(e);c!=null&&c.style&&f&&(a==null?c.style.removeProperty(f):c.style.setProperty(f,a))},{immediate:!0}),i}function Ig(e,t,n={}){const{window:o=wt}=n;return mc(e,t,o==null?void 0:o.localStorage,n)}const gc="--vueuse-safe-area-top",vc="--vueuse-safe-area-right",yc="--vueuse-safe-area-bottom",Ec="--vueuse-safe-area-left";function Rg(){const e=Le(""),t=Le(""),n=Le(""),o=Le("");if(lc){const r=fs(gc),i=fs(vc),l=fs(yc),u=fs(Ec);r.value="env(safe-area-inset-top, 0px)",i.value="env(safe-area-inset-right, 0px)",l.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",s(),Pe("resize",mg(s),{passive:!0})}function s(){e.value=ds(gc),t.value=ds(vc),n.value=ds(yc),o.value=ds(Ec)}return{top:e,right:t,bottom:n,left:o,update:s}}function ds(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function kg(e={}){const{window:t=wt,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:s=!0,includeScrollbar:r=!0,type:i="inner"}=e,l=Le(n),u=Le(o),a=()=>{if(t)if(i==="outer")l.value=t.outerWidth,u.value=t.outerHeight;else if(i==="visual"&&t.visualViewport){const{width:f,height:h,scale:d}=t.visualViewport;l.value=Math.round(f*d),u.value=Math.round(h*d)}else r?(l.value=t.innerWidth,u.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};a(),Xr(a);const c={passive:!0};if(Pe("resize",a,c),t&&i==="visual"&&t.visualViewport&&Pe(t.visualViewport,"resize",a,c),s){const f=pc("(orientation: portrait)");Ye(f,()=>a())}return{width:l,height:u}}Le();const Ng="__vue-devtools-theme__";function Vg(e={}){const t=Pg({...e,storageKey:Ng});return{colorMode:t,isDark:_e(()=>t.value==="dark")}}function Lg(e,t){const n=Ve();function o(){return n.value||(n.value=document.createElement("iframe"),n.value.id="vue-devtools-iframe",n.value.src=e,n.value.setAttribute("data-v-inspector-ignore","true"),n.value.onload=t),n.value}return{getIframe:o,iframe:n}}const Zr=Ig("__vue-devtools-frame-state__",{width:80,height:60,top:0,left:50,open:!1,route:"/",position:"bottom",isFirstVisit:!0,closeOnOutsideClick:!1,minimizePanelInactive:5e3,preferShowFloatingPanel:!0,reduceMotion:!1});function ps(){function e(t){Zr.value={...Zr.value,...t}}return{state:Cn(Zr),updateState:e}}function Mg(){const{state:e,updateState:t}=ps(),n=_e({get(){return e.value.open},set(r){t({open:r})}}),o=(r,i)=>{n.value=i??!n.value},s=()=>{n.value&&(n.value=!1)};return Rn(()=>{Pe(window,"keydown",r=>{r.code==="KeyD"&&r.altKey&&r.shiftKey&&o()})}),{panelVisible:n,togglePanelVisible:o,closePanel:s}}function hs(e,t,n){return Math.min(Math.max(e,t),n)}const $g=()=>navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome");function _s(e){return typeof e=="string"?e.endsWith("px")?+e.slice(0,-2):+e:e}function bc(e){return e<5?0:e>95?100:Math.abs(e-50)<2?50:e}function Ug(e){const{width:t,height:n}=kg(),{state:o,updateState:s}=ps(),r=Ve(!1),i=Ve(!1),l=rn({x:0,y:0}),u=rn({x:0,y:0}),a=rn({left:10,top:10,right:10,bottom:10});let c=null;const f=Rg();qs(()=>{a.left=_s(f.left.value)+10,a.top=_s(f.top.value)+10,a.right=_s(f.right.value)+10,a.bottom=_s(f.bottom.value)+10});const h=b=>{i.value=!0;const{left:P,top:U,width:H,height:W}=e.value.getBoundingClientRect();l.x=b.clientX-P-H/2,l.y=b.clientY-U-W/2},d=()=>{r.value=!0,!(o.value.minimizePanelInactive<0)&&(c&&clearTimeout(c),c=setTimeout(()=>{r.value=!1},+o.value.minimizePanelInactive||0))};Rn(()=>{d()}),Pe("pointerup",()=>{i.value=!1}),Pe("pointerleave",()=>{i.value=!1}),Pe("pointermove",b=>{if(!i.value)return;const P=t.value/2,U=n.value/2,H=b.clientX-l.x,W=b.clientY-l.y;u.x=H,u.y=W;const $=Math.atan2(W-U,H-P),I=70,R=Math.atan2(0-U+I,0-P),F=Math.atan2(0-U+I,t.value-P),z=Math.atan2(n.value-I-U,0-P),Q=Math.atan2(n.value-I-U,t.value-P);s({position:$>=R&&$<=F?"top":$>=F&&$<=Q?"right":$>=Q&&$<=z?"bottom":"left",left:bc(H/t.value*100),top:bc(W/n.value*100)})});const _=_e(()=>o.value.position==="left"||o.value.position==="right"),v=_e(()=>{if(o.value.minimizePanelInactive<0)return!1;if(o.value.minimizePanelInactive===0)return!0;const b="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0;return!i.value&&!o.value.open&&!r.value&&!b&&o.value.minimizePanelInactive}),y=_e(()=>{var W,$;const b=(((W=e.value)==null?void 0:W.clientWidth)||0)/2,P=((($=e.value)==null?void 0:$.clientHeight)||0)/2,U=o.value.left*t.value/100,H=o.value.top*n.value/100;switch(o.value.position){case"top":return{left:hs(U,b+a.left,t.value-b-a.right),top:a.top+P};case"right":return{left:t.value-a.right-P,top:hs(H,b+a.top,n.value-b-a.bottom)};case"left":return{left:a.left+P,top:hs(H,b+a.top,n.value-b-a.bottom)};case"bottom":default:return{left:hs(U,b+a.left,t.value-b-a.right),top:n.value-a.bottom-P}}}),g=_e(()=>({left:`${y.value.left}px`,top:`${y.value.top}px`})),x=_e(()=>{var Z;u.x,u.y;const b=(((Z=e.value)==null?void 0:Z.clientHeight)||0)/2,P={left:a.left+b,top:a.top+b,right:a.right+b,bottom:a.bottom+b},U=P.left+P.right,H=P.top+P.bottom,W=t.value-U,$=n.value-H,I={zIndex:-1,pointerEvents:i.value?"none":"auto",width:`min(${o.value.width}vw, calc(100vw - ${U}px))`,height:`min(${o.value.height}vh, calc(100vh - ${H}px))`},R=y.value,F=Math.min(W,o.value.width*t.value/100),z=Math.min($,o.value.height*n.value/100),Q=(R==null?void 0:R.left)||0,Ee=(R==null?void 0:R.top)||0;switch(o.value.position){case"top":case"bottom":I.left=0,I.transform="translate(-50%, 0)",Q-P.left<F/2?I.left=`${F/2-Q+P.left}px`:t.value-Q-P.right<F/2&&(I.left=`${t.value-Q-F/2-P.right}px`);break;case"right":case"left":I.top=0,I.transform="translate(0, -50%)",Ee-P.top<z/2?I.top=`${z/2-Ee+P.top}px`:n.value-Ee-P.bottom<z/2&&(I.top=`${n.value-Ee-z/2-P.bottom}px`);break}switch(o.value.position){case"top":I.top=0;break;case"right":I.right=0;break;case"left":I.left=0;break;case"bottom":default:I.bottom=0;break}return I}),D=_e(()=>{const b={transform:_.value?`translate(${v.value?`calc(-50% ${o.value.position==="right"?"+":"-"} 15px)`:"-50%"}, -50%) rotate(90deg)`:`translate(-50%, ${v.value?`calc(-50% ${o.value.position==="top"?"-":"+"} 15px)`:"-50%"})`};if(v.value)switch(o.value.position){case"top":case"right":b.borderTopLeftRadius="0",b.borderTopRightRadius="0";break;case"bottom":case"left":b.borderBottomLeftRadius="0",b.borderBottomRightRadius="0";break}return i.value&&(b.transition="none !important"),b});return{isHidden:v,isDragging:i,isVertical:_,anchorStyle:g,iframeStyle:x,panelStyle:D,onPointerDown:h,bringUp:d}}const ms=20,gs=100,Fg=cn({__name:"FrameBox",props:{isDragging:{type:Boolean},client:{},viewMode:{}},setup(e){const t=e,{state:n,updateState:o}=ps(),s=Ve(),r=Ve(!1);bm(()=>{Oa.functions.on("update-client-state",l=>{l&&o({minimizePanelInactive:l.minimizePanelInteractive,closeOnOutsideClick:l.closeOnOutsideClick,preferShowFloatingPanel:l.showFloatingPanel,reduceMotion:l.reduceMotion})})}),qs(()=>{if(s.value&&n.value.open){const l=t.client.getIFrame();l.style.pointerEvents=r.value||t.isDragging?"none":"auto",Array.from(s.value.children).every(u=>u!==l)&&s.value.appendChild(l)}}),Pe(window,"keydown",l=>{}),Pe(window,"mousedown",l=>{if(!n.value.closeOnOutsideClick||!n.value.open||r.value)return;l.composedPath().find(a=>{var f;const c=a;return Array.from(c.classList||[]).some(h=>h.startsWith("vue-devtools"))||((f=c.tagName)==null?void 0:f.toLowerCase())==="iframe"})||o({open:!1})}),Pe(window,"mousemove",l=>{if(!r.value||!n.value.open)return;const a=t.client.getIFrame().getBoundingClientRect();if(r.value.right){const f=Math.abs(l.clientX-((a==null?void 0:a.left)||0))/window.innerWidth*100;o({width:Math.min(gs,Math.max(ms,f))})}else if(r.value.left){const f=Math.abs(((a==null?void 0:a.right)||0)-l.clientX)/window.innerWidth*100;o({width:Math.min(gs,Math.max(ms,f))})}if(r.value.top){const f=Math.abs(((a==null?void 0:a.bottom)||0)-l.clientY)/window.innerHeight*100;o({height:Math.min(gs,Math.max(ms,f))})}else if(r.value.bottom){const f=Math.abs(l.clientY-((a==null?void 0:a.top)||0))/window.innerHeight*100;o({height:Math.min(gs,Math.max(ms,f))})}}),Pe(window,"mouseup",()=>{r.value=!1}),Pe(window,"mouseleave",()=>{r.value=!1});const i=_e(()=>t.viewMode==="xs"?"view-mode-xs":t.viewMode==="fullscreen"?"view-mode-fullscreen":"");return(l,u)=>tt((Re(),Ct("div",{ref_key:"container",ref:s,class:ht(["vue-devtools-frame",i.value])},[tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{top:0},onMousedown:u[0]||(u[0]=Pt(()=>r.value={top:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{bottom:0},onMousedown:u[1]||(u[1]=Pt(()=>r.value={bottom:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{left:0},onMousedown:u[2]||(u[2]=Pt(()=>r.value={left:!0},["prevent"]))},null,544),[[st,J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{right:0},onMousedown:u[3]||(u[3]=Pt(()=>r.value={right:!0},["prevent"]))},null,544),[[st,J(n).position!=="right"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,left:0,cursor:"nwse-resize"},onMousedown:u[4]||(u[4]=Pt(()=>r.value={top:!0,left:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"&&J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,right:0,cursor:"nesw-resize"},onMousedown:u[5]||(u[5]=Pt(()=>r.value={top:!0,right:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"&&J(n).position!=="right"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,left:0,cursor:"nesw-resize"},onMousedown:u[6]||(u[6]=Pt(()=>r.value={bottom:!0,left:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"&&J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,right:0,cursor:"nwse-resize"},onMousedown:u[7]||(u[7]=Pt(()=>r.value={bottom:!0,right:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"&&J(n).position!=="right"]])],2)),[[st,J(n).open]])}}),wc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n},Bg=wc(Fg,[["__scopeId","data-v-399f5059"]]),Hg=wc(cn({__name:"App",setup(e){const t=Ve(),n=Ve(),{colorMode:o}=Vg({selector:t}),s=Ve({viewMode:"default"}),r=_e(()=>{const $=o.value==="dark";return{"--vue-devtools-widget-bg":$?"#121212":"#ffffff","--vue-devtools-widget-fg":$?"#F5F5F5":"#111","--vue-devtools-widget-border":$?"#3336":"#efefef","--vue-devtools-widget-shadow":$?"rgba(0,0,0,0.3)":"rgba(128,128,128,0.1)"}}),{onPointerDown:i,bringUp:l,anchorStyle:u,iframeStyle:a,isDragging:c,isVertical:f,isHidden:h,panelStyle:d}=Ug(n),{togglePanelVisible:_,closePanel:v,panelVisible:y}=Mg(),g=_m(),x=Ve(!0);O.__VUE_DEVTOOLS_TOGGLE_OVERLAY__=$=>{x.value=$};const{state:D}=ps();function b($,I=50,R=200){return new Promise(F=>{var z;(z=$==null?void 0:$.contentWindow)==null||z.postMessage("__VUE_DEVTOOLS_CREATE_CLIENT__","*"),window.addEventListener("message",Q=>{Q.data==="__VUE_DEVTOOLS_CLIENT_READY__"&&F()})})}const P=Ve();Rh(()=>{wa().functions.on("toggle-panel",(I=!y)=>{_(void 0,I)}),Sa.ctx.api.getVueInspector().then(I=>{P.value=I;let R=y.value;P.value.onEnabled=()=>{R=y.value,_(void 0,!1)},P.value.onDisabled=()=>{_(void 0,R)}})}),addEventListener("keyup",$=>{var I,R,F;((I=$.key)==null?void 0:I.toLowerCase())==="escape"&&((R=P.value)!=null&&R.enabled)&&((F=P.value)==null||F.disable())});const U=_e(()=>!!P.value);function H(){P.value.enable()}const{getIframe:W}=Lg(g,async()=>{const $=W();dm($),await b($)});return($,I)=>tt((Re(),Ct("div",{ref_key:"anchorEle",ref:t,class:ht(["vue-devtools__anchor",{"vue-devtools__anchor--vertical":J(f),"vue-devtools__anchor--hide":J(h),fullscreen:s.value.viewMode==="fullscreen","reduce-motion":J(D).reduceMotion}]),style:Ne([J(u),r.value]),onMousemove:I[2]||(I[2]=(...R)=>J(l)&&J(l)(...R))},[J($g)()?$o("",!0):(Re(),Ct("div",{key:0,class:"vue-devtools__anchor--glowing",style:Ne(J(c)?"opacity: 0.6 !important":"")},null,4)),ie("div",{ref_key:"panelEle",ref:n,class:"vue-devtools__panel",style:Ne(J(d)),onPointerdown:I[1]||(I[1]=(...R)=>J(i)&&J(i)(...R))},[ie("div",{class:"vue-devtools__anchor-btn panel-entry-btn",title:"Toggle Vue DevTools","aria-label":"Toggle devtools panel",style:Ne(J(y)?"":"filter:saturate(0)"),onClick:I[0]||(I[0]=(...R)=>J(_)&&J(_)(...R))},I[3]||(I[3]=[ie("svg",{viewBox:"0 0 256 198",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[ie("path",{fill:"#41B883",d:"M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"}),ie("path",{fill:"#41B883",d:"m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"}),ie("path",{fill:"#35495E",d:"M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"})],-1)]),4),J(Sa).ctx.state.vitePluginDetected&&U.value?(Re(),Ct(Ie,{key:0},[I[5]||(I[5]=ie("div",{class:"vue-devtools__panel-content vue-devtools__panel-divider"},null,-1)),ie("div",{class:ht(["vue-devtools__anchor-btn vue-devtools__panel-content vue-devtools__inspector-button",{active:U.value}]),title:"Toggle Component Inspector",onClick:H},[(Re(),Ct("svg",{xmlns:"http://www.w3.org/2000/svg",style:Ne([{height:"1.1em",width:"1.1em",opacity:"0.5"},U.value?"opacity:1;color:#00dc82;":""]),viewBox:"0 0 24 24"},I[4]||(I[4]=[ie("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[ie("circle",{cx:"12",cy:"12",r:".5",fill:"currentColor"}),ie("path",{d:"M5 12a7 7 0 1 0 14 0a7 7 0 1 0-14 0m7-9v2m-9 7h2m7 7v2m7-9h2"})],-1)]),4))],2)],64)):$o("",!0)],36),Se(Bg,{style:Ne(J(a)),"is-dragging":J(c),client:{close:J(v),getIFrame:J(W)},"view-mode":s.value.viewMode},null,8,["style","is-dragging","client","view-mode"])],38)),[[st,J(D).preferShowFloatingPanel?x.value:J(y)]])}}),[["__scopeId","data-v-640ec535"]]);function zg(e){const t="__vue-devtools-container__",n=document.createElement("div");n.setAttribute("id",t),n.setAttribute("data-v-inspector-ignore","true"),document.getElementsByTagName("body")[0].appendChild(n),dp({render:()=>Ud(e),devtools:{hide:!0}}).mount(n)}zg(Hg)})();
