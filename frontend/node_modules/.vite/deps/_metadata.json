{"hash": "76a8d8ad", "configHash": "26e765ce", "lockfileHash": "6b4e58d5", "browserHash": "87b7b4f0", "optimized": {"class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "ead6206e", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a77e0997", "needsInterop": false}, "dompurify": {"src": "../../dompurify/dist/purify.es.mjs", "file": "dompurify.js", "fileHash": "48b9e94e", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "5a4e6b2f", "needsInterop": false}, "marked": {"src": "../../marked/lib/marked.esm.js", "file": "marked.js", "fileHash": "31ab5be6", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "a442d553", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "9b44920e", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "3e762e44", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "517721e6", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-VZXQDS5F": {"file": "chunk-VZXQDS5F.js"}, "chunk-PZ5AY32C": {"file": "chunk-PZ5AY32C.js"}}}