{"hash": "4df931aa", "configHash": "26e765ce", "lockfileHash": "26a9c29c", "browserHash": "b6b109c4", "optimized": {"class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c7a7fd49", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "29a89f3b", "needsInterop": false}, "dompurify": {"src": "../../dompurify/dist/purify.es.mjs", "file": "dompurify.js", "fileHash": "477b97a1", "needsInterop": false}, "lucide-vue-next": {"src": "../../lucide-vue-next/dist/esm/lucide-vue-next.js", "file": "lucide-vue-next.js", "fileHash": "d4d97d8e", "needsInterop": false}, "marked": {"src": "../../marked/lib/marked.esm.js", "file": "marked.js", "fileHash": "f556be93", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "bf9d1a30", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "68f39772", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "bda18bf2", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "c398f6d1", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-VZXQDS5F": {"file": "chunk-VZXQDS5F.js"}, "chunk-PZ5AY32C": {"file": "chunk-PZ5AY32C.js"}}}