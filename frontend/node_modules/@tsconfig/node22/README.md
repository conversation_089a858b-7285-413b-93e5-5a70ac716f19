### A base TSConfig for working with Node 22.

Add the package to your `"devDependencies"`:

```sh
npm install --save-dev @tsconfig/node22
yarn add --dev @tsconfig/node22
```

Add to your `tsconfig.json`:

```json
"extends": "@tsconfig/node22/tsconfig.json"
```

---

The `tsconfig.json`: 

```jsonc
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "_version": "22.0.0",

  "compilerOptions": {
    "lib": ["es2024", "ESNext.Array", "ESNext.Collection", "ESNext.Iterator"],
    "module": "nodenext",
    "target": "es2022",

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "moduleResolution": "node16"
  }
}

```

You can find the [code here](https://github.com/tsconfig/bases/blob/master/bases/node22.json).
