{"name": "dlv", "version": "1.1.3", "description": "Safely get a dot-notated property within an object.", "main": "dist/dlv.js", "browser": "dist/dlv.umd.js", "module": "dist/dlv.es.js", "scripts": {"dev": "microbundle watch", "build": "microbundle", "prepublish": "npm run build", "test": "node test", "release": "npm run build && npm test && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish"}, "keywords": ["delve", "dot notation", "dot"], "files": ["index.js", "dist"], "author": "<PERSON> <<EMAIL>> (http://jasonformat.com)", "repository": "developit/dlv", "license": "MIT", "devDependencies": {"microbundle": "^0.11.0"}}